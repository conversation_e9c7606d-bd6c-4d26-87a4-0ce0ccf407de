<documentation title="Function Argument Spacing">
    <standard>
    <![CDATA[
    Function arguments should have one space after a comma, and single spaces surrounding the equals sign for default values.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Single spaces after a comma.">
        <![CDATA[
function foo($bar,<em> </em>$baz)
{
}
        ]]>
        </code>
        <code title="Invalid: No spaces after a comma.">
        <![CDATA[
function foo($bar,<em></em>$baz)
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Single spaces around an equals sign in function declaration.">
        <![CDATA[
function foo($bar, $baz<em> </em>=<em> </em>true)
{
}
        ]]>
        </code>
        <code title="Invalid: No spaces around an equals sign in function declaration.">
        <![CDATA[
function foo($bar, $baz<em></em>=<em></em>true)
{
}
        ]]>
        </code>
    </code_comparison>
</documentation>
