<?php

declare( strict_types = 1 );

namespace Pm\Module\Artist\Queries;

use Pm\Module\Artist\Dto\ArtistData;
use Pm\Module\Artist\Dto\ArtistDataWithAlbum;
use Pm\Module\Artist\Dto\ProfileData;
use Pm\Module\Artist\Table\ArtistAttributeTable;
use Pm\Module\Avatar\Utils\AvatarUtil;
use Pm\Module\Helper\QueryHelper;
use Pm\Module\Helper\ResponseHelper;
use Pm\Module\Helper\UrlHelper;
use Pm\PmConfig;

/**
 * Artist query utilities.
 */
final class ArtistQuery
{
	private string $artistAttributesTable = 'pm_artist_attributes';

	private ArtistQueryCommand $artistQueryCommand;

	private string $artistBlocksTable = 'pm_artist_blocks';

	private bool $isLoggedIn;

	private int $currentUserId;

	private ?AvatarUtil $avatarUtil;

	private array $currentUserFollowedUsers = [];

	private string $columnAliases;

	private string $uploadsUrl;

	private string $profileUrlTag;

	private bool $useInstagram = false;

	private bool $useFollowingStatus = false;

	private bool $useTotalFollowing = false;

	public function __construct()
	{

		global $wpdb;

		$this->artistAttributesTable = $wpdb->prefix . $this->artistAttributesTable;
		$this->artistBlocksTable     = $wpdb->prefix . $this->artistBlocksTable;
		$this->artistQueryCommand    = new ArtistQueryCommand();

		$this->columnAliases = $this->artistQueryCommand->columnAliases();

		$this->isLoggedIn    = is_user_logged_in();
		$this->currentUserId = get_current_user_id();
		$this->uploadsUrl    = site_url( 'wp-content/uploads' );
		$this->profileUrlTag = site_url( '/profile/%#portraitmode#%/' );

		$this->avatarUtil = new AvatarUtil( 0 );

		if ( ! $this->isLoggedIn ) {
			$this->columnAliases = str_ireplace( ', CASE WHEN BLOCKEDJOIN.id IS NOT NULL THEN 1 ELSE 0 END AS isBlocked', '', $this->columnAliases );
			$this->columnAliases = str_ireplace( ', CASE WHEN BLOCKINGJOIN.id IS NOT NULL THEN 1 ELSE 0 END AS isBlocking', '', $this->columnAliases );
		}

	}

	/**
	 * Find an artist based on the given parameters.
	 *
	 * @param int  $id                 The artist's user ID.
	 * @param bool $useDescription     (Optional) Whether to include description as part of the returned property or
	 *                                 not.
	 * @param bool $useInstagram       (Optional) Whether to include instagram as part of the returned property or not.
	 * @param bool $useFollowingStatus (Optional) Whether or not to use the follow button.
	 * @param bool $useTotalFollowing  (Optional) Whether to include total_following as part of the returned property
	 *                                 or not.
	 * @param bool $useTotalFollowers  (Optional) Whether to include total_followers as part of the returned property
	 *                                 or not.
	 * @param bool $asProfileData      (Optional) Whether to set the individual object as ProfileData or not.
	 *
	 * @return ArtistData|ArtistDataWithAlbum|ProfileData|null The `ArtistData` object,
	 *
	 *                                                         or `ArtistDataWithAlbum` when `$useAlbum` is `true`,
	 *
	 *                                                         or `ProfileData` object when `$asProfileData` is `true`,
	 *
	 *                                                         or `null` on failure.
	 */
	public function find(
		int $id = 0,
		bool $useDescription = false,
		bool $useInstagram = false,
		bool $useFollowingStatus = false,
		bool $useTotalFollowing = false,
		bool $useTotalFollowers = false,
		bool $asProfileData = false,
		bool $useAlbum = false,
	): ArtistData|ArtistDataWithAlbum|ProfileData|null {
		if ( $asProfileData ) {
			$useInstagram = true;
		}

		$this->useInstagram       = $useInstagram;
		$this->useFollowingStatus = $useFollowingStatus && $this->isLoggedIn;
		$this->useTotalFollowing  = $useTotalFollowing;

		if ( $this->useFollowingStatus ) {
			$this->setCurrentUserFollowedUsers();
		}

		global $wpdb;

		$select = $this->columnAliases;

		if ( ! $this->useInstagram ) {
			$select = str_replace( ', AA.instagram', '', $select );
		}

		if ( ! $useDescription ) {
			$select = str_replace( ', AA.description', '', $select );
		}

		if ( ! $this->useTotalFollowing ) {
			$select = str_replace( ', AA.followed_users AS followedUsers', '', $select );
		}

		$query_command = "
			SELECT $select
			FROM $wpdb->users U
			" . $this->artistQueryCommand->artistAttrJoinCommand() . '
			'
			.
				(
				$this->isLoggedIn ?
					$this->artistQueryCommand->blockedJoinCommand( $this->currentUserId )
					:
					''
				)
				.
				(
				$this->isLoggedIn ?
					$this->artistQueryCommand->blockingJoinCommand( $this->currentUserId )
					:
					''
				)
			.
			'
			WHERE U.ID = %d
		';

		$prepared_command = $wpdb->prepare(
			$query_command,
			$id
		);

		$result = $wpdb->get_row( $prepared_command );

		if ( empty( $result ) ) {
			return null;
		}

		$result = $this->addMissingProps( $result );

		if ( $useTotalFollowers ) {
			$result = $this->setTotalFollowers( $result );
		}

		$transformer = new ArtistQueryResultTransformer();

		if ( $asProfileData ) {
			return $transformer->toProfileData( $result );
		}

		if ( $useAlbum ) {
			return $transformer->toArtistDataWithAlbum( $result );
		}

		return $transformer->toArtistData( $result );
	}

	/**
	 * Get list of artists based on the given parameters.
	 *
	 * @param int         $limit              The number of photos to return.
	 * @param int|null    $lastId             (Optional) The last retrieved user ID. This is used to paginate the
	 *                                        results.
	 * @param int|null    $lastTotalPhotos    (Optional) The last retrieved artist's total Photos.
	 *                                        This is used to paginate the results when `$orderBy` value is
	 *                                        `"most_uploads"`.
	 * @param string|null $includeIds         (Optional) Comma separated list of artist ids to include. E.g: "1,2,3"
	 * @param string|null $excludeIds         (Optional) Comma separated list of artist ids to exclude. E.g: "1,2,3"
	 * @param string      $orderBy            (Optional) The order by. Default to "most_uploads".
	 * @param bool        $useDescription     (Optional) Whether to include description as part of the returned
	 *                                        property or not.
	 * @param bool        $useInstagram       (Optional) Whether to include instagram as part of the returned property
	 *                                        or not.
	 * @param bool        $useFollowingStatus (Optional) Whether or not to use the follow button.
	 * @param bool        $useTotalFollowing  (Optional) Whether to include total_following as part of the returned
	 *                                        property or not.
	 * @param bool        $useTotalFollowers  (Optional) Whether to include total_followers as part of the returned
	 *                                        property or not.
	 * @param bool        $asProfileData      (Optional) Whether to set the individual object as ProfileData or not.
	 * @param bool        $isBlocked          (Optional) Whether to only query blocked users or not.
	 *
	 * @return ArtistData[]|ArtistDataWithAlbum[]|ProfileData[] Array of `ArtistData` instances,
	 *
	 *                                                         or array of `ArtistDataWithAlbum` instances when
	 *                                                         `$useAlbum` is `true`,
	 *
	 *                                                         or array of `ProfileData`instances when `$asProfileData`
	 *                                                         is `true`.
	 */
	public function fetch(
		int $limit = 15,
		?int $lastId = null,
		?int $lastTotalPhotos = null,
		?string $includeIds = null,
		?string $excludeIds = null,
		string $orderBy = 'most_uploads',
		bool $useDescription = false,
		bool $useInstagram = false,
		bool $useFollowingStatus = false,
		bool $useTotalFollowing = false,
		bool $useTotalFollowers = false,
		bool $asProfileData = false,
		?bool $isBlocked = null,
	): array {
		if ( $asProfileData ) {
			$useInstagram = true;
		}

		$this->useInstagram       = $useInstagram;
		$this->useFollowingStatus = $useFollowingStatus && $this->isLoggedIn;
		$this->useTotalFollowing  = $useTotalFollowing;

		if ( $this->useFollowingStatus ) {
			$this->setCurrentUserFollowedUsers();
		}

		global $wpdb;

		$photo_join_command = '';

		if ( $orderBy === 'recently_uploaded' ) {
			$photo_join_command = $this->artistQueryCommand->photoJoinCommand();
		}

		$last_id_where_clause = '';

		if ( $lastId !== null ) {
			$lastId = absint( $lastId );
		}

		/**
		 * The use of "<" sign is because we're using DESC order.
		 * We don't use %d placeholder for the lastId for simplicity.
		 */
		if ( $lastId !== null ) {
			if ( $orderBy === 'registered' ) {
				$last_id_where_clause = "AND U.ID < $lastId";
			} elseif ( $orderBy === 'recently_uploaded' ) {
				$latest_photo_id = ( new ArtistAttributeTable( $lastId ) )->value( 'latest_photo_id' );
				$latest_photo_id = absint( $latest_photo_id );

				$last_id_where_clause = "AND P.ID < $latest_photo_id";
			}
		}

		$include_ids_where_clause = '';

		if ( $includeIds !== null ) {
			$includeIds = QueryHelper::sanitizeIds( $includeIds );

			$include_ids_where_clause = $includeIds ? "AND U.ID IN [parentheses_quote]($includeIds)[/parentheses_quote]" : '';
		}

		$exclude_ids_where_clause = '';

		if ( $excludeIds !== null ) {
			$excludeIds = QueryHelper::sanitizeIds( $excludeIds );

			$exclude_ids_where_clause = $excludeIds ? "AND U.ID NOT IN [parentheses_quote]($excludeIds)[/parentheses_quote]" : '';
		}

		// All of %s will be removed in the prepared command.
		$total_photos_where_clause = '%s %s %s';

		if ( $lastTotalPhotos !== null && $orderBy === 'most_uploads' ) {
			if ( $lastId !== null ) {
				$total_photos_where_clause = 'AND AA.total_photos <= %d and not (AA.total_photos = %d and U.ID >= %d)';
			} else {
				// Last 2 %s will be removed in the prepared command.
				$total_photos_where_clause = 'AND AA.total_photos <= %d %s %s';
			}
		}

		$blocked_where_clause = '';

		if ( $this->isLoggedIn && $isBlocked ) {
			$blocked_where_clause = 'AND BLOCKEDJOIN.blocked_id = U.ID';
		}

		$order_by_clause = match ( $orderBy ) {
			'most_uploads' => 'ORDER BY ABS(AA.total_photos) DESC, U.ID DESC',
			'recently_uploaded' => 'ORDER BY P.post_date DESC',
			default => 'ORDER BY U.ID DESC',
		};

		$select = $this->columnAliases;

		if ( ! $this->useInstagram ) {
			$select = str_replace( ', AA.instagram', '', $select );
		}

		if ( ! $useDescription ) {
			$select = str_replace( ', AA.description', '', $select );
		}

		$query_command = "
			SELECT $select
			FROM $wpdb->users U
			" . $this->artistQueryCommand->artistAttrJoinCommand() . '
			' . $photo_join_command . '
			'
				.
				(
				$this->isLoggedIn ?
					$this->artistQueryCommand->blockedJoinCommand( $this->currentUserId, $isBlocked )
					:
					''
				)
				.
				(
				$this->isLoggedIn ?
					$this->artistQueryCommand->blockingJoinCommand( $this->currentUserId )
					:
					''
				)
				.
			'
			WHERE AA.total_photos > ' . ( PmConfig::minPhotoUploads() - 1 ) . '
			' . $last_id_where_clause . '
			' . $include_ids_where_clause . '
			' . $exclude_ids_where_clause . '
			' . $total_photos_where_clause . '
			' . $blocked_where_clause . '
			' . $order_by_clause . '
			LIMIT %d
		';

		$query_command = QueryHelper::removeParenthesesQuotes( $query_command );

		$prepared_command = $wpdb->prepare(
			$query_command,
			( $lastTotalPhotos !== null && $orderBy === 'most_uploads' ? $lastTotalPhotos : '[empty_quotes]' ),
			( $lastTotalPhotos !== null && $orderBy === 'most_uploads' ? $lastTotalPhotos : '[empty_quotes]' ),
			( $lastTotalPhotos !== null && $orderBy === 'most_uploads' && $lastId !== null ? $lastId : '[empty_quotes]' ),
			$limit
		);

		$prepared_command = QueryHelper::removeEmptyQuotes( $prepared_command );

		$results = $wpdb->get_results( $prepared_command );

		if ( empty( $results ) ) {
			return [];
		}

		foreach ( $results as &$result ) {
			$result = $this->addMissingProps( $result );
		}

		if ( $useTotalFollowers ) {
			$results = $this->setArtistsTotalFollowers( $results );
		}

		$artists = array_map( function ( object $result ) use ( $asProfileData ): ArtistData|ProfileData {
			$transformer = new ArtistQueryResultTransformer();
			if ( $asProfileData ) {
				return $transformer->toProfileData( $result );
			}

			return $transformer->toArtistData( $result );
		}, $results );

		return array_values( $artists );
	}

	public function setUseFollowingStatus( bool $useFollowingStatus ): void
	{
		$this->useFollowingStatus = $useFollowingStatus;
	}

	public function setUseTotalFollowing( bool $useTotalFollowing ): void
	{
		$this->useTotalFollowing = $useTotalFollowing;
	}

	public function setCurrentUserFollowedUsers(): void
	{
		$followed_users = ArtistAttributeTable::withId( $this->currentUserId )->value( 'followed_users' );

		$this->currentUserFollowedUsers = $followed_users ?? [];
	}

	public function addMissingProps( object $query_result ): object
	{
		$query_result = ResponseHelper::castNumericToInt( $query_result, $this->artistQueryCommand->intProps() );
		$query_result = ResponseHelper::castToBool( $query_result, $this->artistQueryCommand->boolProps() );

		$query_result->profileUrl = str_ireplace( '%#portraitmode#%', $query_result->nicename, $this->profileUrlTag );

		$query_result->totalFollowing = 0;

		if ( $this->useTotalFollowing ) {
			$followed_users = $query_result->followedUsers ? json_decode( $query_result->followedUsers, true ) : [];
			$followed_users = empty( $followed_users ) || ! is_array( $followed_users ) ? [] : $followed_users;
			$followed_users = array_values( $followed_users );
			$followed_users = array_map( 'absint', $followed_users );

			$query_result->followedUsers  = $followed_users;
			$query_result->totalFollowing = count( $query_result->followedUsers );
		}

		$query_result->isFollowing = false;

		if ( $this->useFollowingStatus ) {
			$query_result->isFollowing = in_array( $query_result->id, $this->currentUserFollowedUsers, true );
		}

		$query_result->totalFollowers = 0;

		if ( empty( $query_result->avatarUrl ) ) {
			$this->avatarUtil->setArtistId( $query_result->id );
			$query_result->avatarUrl = $this->avatarUtil->getGravatarUrl( $query_result->email );
		}

		// if ( str_contains( $query_result->avatarUrl, $this->uploadsUrl ) ) {
		//  $query_result->avatarUrl = str_ireplace( $this->uploadsUrl, UrlHelper::mediaOffloadBaseUrl(), $query_result->avatarUrl );
		// }

		if ( property_exists( $query_result, 'latestPhotoUrl' ) ) {
			$query_result->latestPhotoUrl = str_ireplace( $this->uploadsUrl, UrlHelper::mediaOffloadBaseUrl(), $query_result->latestPhotoUrl );
		}

		$query_result->membershipType = pm_artist_membership_type( $query_result->id );

		return $query_result;
	}

	/**
	 * Set the number of total followers for the artist query result.
	 *
	 * @param object $query_result The artist query result.
	 *
	 * @return object The of artist query result with total followers property.
	 */
	public function setTotalFollowers( object $query_result ): object
	{
		global $wpdb;

		// Get total records in the artist attributes table where the artist ID is in the followed users array.
		$query_command = "
			SELECT COUNT(*)
			FROM $this->artistAttributesTable
			WHERE JSON_CONTAINS(
				followed_users,
				%s,
				'$'
			)
		";

		$prepared_command = $wpdb->prepare(
			$query_command,
			$query_result->id
		);

		$total_followers = $wpdb->get_var( $prepared_command );

		if ( empty( $total_followers ) ) {
			$query_result->totalFollowers = 0;
		} else {
			$query_result->totalFollowers = (int) $total_followers;
		}

		return $query_result;
	}

	/**
	 * Set the number of total followers for each artist query result.
	 *
	 * @param array $query_results Array of artist query result.
	 *
	 * @return array Array of artist query result with total followers property.
	 */
	public function setArtistsTotalFollowers( array $query_results ): array
	{
		foreach ( $query_results as &$query_result ) {
			$query_result = $this->setTotalFollowers( $query_result );
		}

		return $query_results;
	}
}
