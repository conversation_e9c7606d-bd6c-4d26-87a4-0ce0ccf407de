<html>
 <head>
  <title>GeneratorTest Coding Standards</title>
  <style>
        body {
            background-color: #FFFFFF;
            font-size: 14px;
            font-family: Arial, Helvetica, sans-serif;
            color: #000000;
        }

        h1 {
            color: #666666;
            font-size: 20px;
            font-weight: bold;
            margin-top: 0px;
            background-color: #E6E7E8;
            padding: 20px;
            border: 1px solid #BBBBBB;
        }

        h2 {
            color: #00A5E3;
            font-size: 16px;
            font-weight: normal;
            margin-top: 50px;
        }

        .code-comparison {
            width: 100%;
        }

        .code-comparison td {
            border: 1px solid #CCCCCC;
        }

        .code-comparison-title, .code-comparison-code {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px;
            color: #000000;
            vertical-align: top;
            padding: 4px;
            width: 50%;
            background-color: #F1F1F1;
            line-height: 15px;
        }

        .code-comparison-title {
            text-align: left;
            font-weight: 600;
        }

        .code-comparison-code {
            font-family: Courier;
            background-color: #F9F9F9;
        }

        .code-comparison-highlight {
            background-color: #DDF1F7;
            border: 1px solid #00A5E3;
            line-height: 15px;
        }

        .tag-line {
            text-align: center;
            width: 100%;
            margin-top: 30px;
            font-size: 12px;
        }

        .tag-line a {
            color: #000000;
        }
    </style>
 </head>
 <body>
  <h1>GeneratorTest Coding Standards</h1>
  <a name="Code-Comparison,-line-length" />
  <h2>Code Comparison, line length</h2>
  <p class="text">Ensure there is no PHP &quot;Warning: str_repeat(): Second argument has to be greater than or equal to 0&quot;.<br/>
Ref: squizlabs/PHP_CodeSniffer#2522</p>
  <table class="code-comparison">
   <tr>
    <th class="code-comparison-title">Valid: contains line which is too long.</th>
    <th class="code-comparison-title">Invalid: contains line which is too long.</th>
   </tr>
   <tr>
    <td class="code-comparison-code">class&nbsp;Foo&nbsp;extends&nbsp;Bar&nbsp;implements&nbsp;<span class="code-comparison-highlight">Countable</span>,&nbsp;Serializable</br>{</br>}</td>
    <td class="code-comparison-code">class&nbsp;Foo&nbsp;extends&nbsp;Bar</br>{</br>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;static&nbsp;function&nbsp;<span class="code-comparison-highlight">foobar</span>($param1,&nbsp;$param2)&nbsp;{}</br>}</td>
   </tr>
  </table>
  <div class="tag-line">Documentation generated on #REDACTED# by <a href="https://github.com/PHPCSStandards/PHP_CodeSniffer">PHP_CodeSniffer #VERSION#</a></div>
 </body>
</html>
