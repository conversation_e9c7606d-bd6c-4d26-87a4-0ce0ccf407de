(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();const Vn=!1,Yn=(e,t)=>e===t,de=Symbol("solid-proxy"),un=typeof Proxy=="function",Je=Symbol("solid-track"),Xe={equals:Yn};let dn=bn;const ye=1,Ze=2,fn={owned:null,cleanups:null,context:null,owner:null},ut={};var j=null;let dt=null,Kn=null,z=null,ne=null,pe=null,st=0;function xe(e,t){const n=z,r=j,s=e.length===0,i=t===void 0?r:t,o=s?fn:{owned:null,cleanups:null,context:i?i.context:null,owner:i},l=s?e:()=>e(()=>re(()=>De(o)));j=o,z=null;try{return he(l,!0)}finally{z=n,j=r}}function U(e,t){t=t?Object.assign({},Xe,t):Xe;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},r=s=>(typeof s=="function"&&(s=s(n.value)),yn(n,s));return[pn.bind(n),r]}function hn(e,t,n){const r=it(e,t,!0,ye);Re(r)}function N(e,t,n){const r=it(e,t,!1,ye);Re(r)}function Gn(e,t,n){dn=nr;const r=it(e,t,!1,ye);r.user=!0,pe?pe.push(r):Re(r)}function H(e,t,n){n=n?Object.assign({},Xe,n):Xe;const r=it(e,t,!0,0);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,Re(r),pn.bind(r)}function Jn(e){return e&&typeof e=="object"&&"then"in e}function Xn(e,t,n){let r,s,i;r=!0,s=e,i={};let o=null,l=ut,a=!1,c="initialValue"in i,u=typeof r=="function"&&H(r);const d=new Set,[h,p]=(i.storage||U)(i.initialValue),[$,v]=U(void 0),[_,O]=U(void 0,{equals:!1}),[k,D]=U(c?"ready":"unresolved");function P(q,R,I,W){return o===q&&(o=null,W!==void 0&&(c=!0),(q===l||R===l)&&i.onHydrated&&queueMicrotask(()=>i.onHydrated(W,{value:R})),l=ut,F(R,I)),R}function F(q,R){he(()=>{R===void 0&&p(()=>q),D(R!==void 0?"errored":c?"ready":"unresolved"),v(R);for(const I of d.keys())I.decrement();d.clear()},!1)}function K(){const q=Qn,R=h(),I=$();if(I!==void 0&&!o)throw I;return z&&z.user,R}function ae(q=!0){if(q!==!1&&a)return;a=!1;const R=u?u():r;if(R==null||R===!1){P(o,re(h));return}let I;const W=l!==ut?l:re(()=>{try{return s(R,{value:h(),refetching:q})}catch(ee){I=ee}});if(I!==void 0){P(o,void 0,Ve(I),R);return}else if(!Jn(W))return P(o,W,void 0,R),W;return o=W,"v"in W?(W.s===1?P(o,W.v,void 0,R):P(o,void 0,Ve(W.v),R),W):(a=!0,queueMicrotask(()=>a=!1),he(()=>{D(c?"refreshing":"pending"),O()},!1),W.then(ee=>P(W,ee,void 0,R),ee=>P(W,void 0,Ve(ee),R)))}Object.defineProperties(K,{state:{get:()=>k()},error:{get:()=>$()},loading:{get(){const q=k();return q==="pending"||q==="refreshing"}},latest:{get(){if(!c)return K();const q=$();if(q&&!o)throw q;return h()}}});let ce=j;return u?hn(()=>(ce=j,ae(!1))):ae(!1),[K,{refetch:q=>Rt(ce,()=>ae(q)),mutate:p}]}function Oe(e){return he(e,!1)}function re(e){if(z===null)return e();const t=z;z=null;try{return e()}finally{z=t}}function Et(e,t,n){const r=Array.isArray(e);let s,i=n&&n.defer;return o=>{let l;if(r){l=Array(e.length);for(let c=0;c<e.length;c++)l[c]=e[c]()}else l=e();if(i)return i=!1,o;const a=re(()=>t(l,s,o));return s=l,a}}function Tt(e){Gn(()=>re(e))}function Te(e){return j===null||(j.cleanups===null?j.cleanups=[e]:j.cleanups.push(e)),e}function bt(){return z}function gn(){return j}function Rt(e,t){const n=j,r=z;j=e,z=null;try{return he(t,!0)}catch(s){Dt(s)}finally{j=n,z=r}}function Zn(e){const t=z,n=j;return Promise.resolve().then(()=>{z=t,j=n;let r;return he(e,!1),z=j=null,r?r.done:void 0})}const[sl,il]=U(!1);function mn(e,t){const n=Symbol("context");return{id:n,Provider:rr(n),defaultValue:e}}function It(e){let t;return j&&j.context&&(t=j.context[e.id])!==void 0?t:e.defaultValue}function Pt(e){const t=H(e),n=H(()=>vt(t()));return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}let Qn;function pn(){if(this.sources&&this.state)if(this.state===ye)Re(this);else{const e=ne;ne=null,he(()=>et(this),!1),ne=e}if(z){const e=this.observers?this.observers.length:0;z.sources?(z.sources.push(this),z.sourceSlots.push(e)):(z.sources=[this],z.sourceSlots=[e]),this.observers?(this.observers.push(z),this.observerSlots.push(z.sources.length-1)):(this.observers=[z],this.observerSlots=[z.sources.length-1])}return this.value}function yn(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&he(()=>{for(let s=0;s<e.observers.length;s+=1){const i=e.observers[s],o=dt&&dt.running;o&&dt.disposed.has(i),(o?!i.tState:!i.state)&&(i.pure?ne.push(i):pe.push(i),i.observers&&vn(i)),o||(i.state=ye)}if(ne.length>1e6)throw ne=[],new Error},!1)),t}function Re(e){if(!e.fn)return;De(e);const t=st;er(e,e.value,t)}function er(e,t,n){let r;const s=j,i=z;z=j=e;try{r=e.fn(t)}catch(o){return e.pure&&(e.state=ye,e.owned&&e.owned.forEach(De),e.owned=null),e.updatedAt=n+1,Dt(o)}finally{z=i,j=s}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?yn(e,r):e.value=r,e.updatedAt=n)}function it(e,t,n,r=ye,s){const i={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:j,context:j?j.context:null,pure:n};return j===null||j!==fn&&(j.owned?j.owned.push(i):j.owned=[i]),i}function Qe(e){if(e.state===0)return;if(e.state===Ze)return et(e);if(e.suspense&&re(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<st);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===ye)Re(e);else if(e.state===Ze){const r=ne;ne=null,he(()=>et(e,t[0]),!1),ne=r}}function he(e,t){if(ne)return e();let n=!1;t||(ne=[]),pe?n=!0:pe=[],st++;try{const r=e();return tr(n),r}catch(r){n||(pe=null),ne=null,Dt(r)}}function tr(e){if(ne&&(bn(ne),ne=null),e)return;const t=pe;pe=null,t.length&&he(()=>dn(t),!1)}function bn(e){for(let t=0;t<e.length;t++)Qe(e[t])}function nr(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:Qe(r)}for(t=0;t<n;t++)Qe(e[t])}function et(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const s=r.state;s===ye?r!==t&&(!r.updatedAt||r.updatedAt<st)&&Qe(r):s===Ze&&et(r,t)}}}function vn(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=Ze,n.pure?ne.push(n):pe.push(n),n.observers&&vn(n))}}function De(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),s=n.observers;if(s&&s.length){const i=s.pop(),o=n.observerSlots.pop();r<s.length&&(i.sourceSlots[o]=r,s[r]=i,n.observerSlots[r]=o)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)De(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)De(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function Ve(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function Dt(e,t=j){throw Ve(e)}function vt(e){if(typeof e=="function"&&!e.length)return vt(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=vt(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function rr(e,t){return function(r){let s;return N(()=>s=re(()=>(j.context={...j.context,[e]:r.value},Pt(()=>r.children))),void 0),s}}const wt=Symbol("fallback");function tt(e){for(let t=0;t<e.length;t++)e[t]()}function sr(e,t,n={}){let r=[],s=[],i=[],o=0,l=t.length>1?[]:null;return Te(()=>tt(i)),()=>{let a=e()||[],c=a.length,u,d;return a[Je],re(()=>{let p,$,v,_,O,k,D,P,F;if(c===0)o!==0&&(tt(i),i=[],r=[],s=[],o=0,l&&(l=[])),n.fallback&&(r=[wt],s[0]=xe(K=>(i[0]=K,n.fallback())),o=1);else if(o===0){for(s=new Array(c),d=0;d<c;d++)r[d]=a[d],s[d]=xe(h);o=c}else{for(v=new Array(c),_=new Array(c),l&&(O=new Array(c)),k=0,D=Math.min(o,c);k<D&&r[k]===a[k];k++);for(D=o-1,P=c-1;D>=k&&P>=k&&r[D]===a[P];D--,P--)v[P]=s[D],_[P]=i[D],l&&(O[P]=l[D]);for(p=new Map,$=new Array(P+1),d=P;d>=k;d--)F=a[d],u=p.get(F),$[d]=u===void 0?-1:u,p.set(F,d);for(u=k;u<=D;u++)F=r[u],d=p.get(F),d!==void 0&&d!==-1?(v[d]=s[u],_[d]=i[u],l&&(O[d]=l[u]),d=$[d],p.set(F,d)):i[u]();for(d=k;d<c;d++)d in v?(s[d]=v[d],i[d]=_[d],l&&(l[d]=O[d],l[d](d))):s[d]=xe(h);s=s.slice(0,o=c),r=a.slice(0)}return s});function h(p){if(i[d]=p,l){const[$,v]=U(d);return l[d]=v,t(a[d],$)}return t(a[d])}}}function ir(e,t,n={}){let r=[],s=[],i=[],o=[],l=0,a;return Te(()=>tt(i)),()=>{const c=e()||[],u=c.length;return c[Je],re(()=>{if(u===0)return l!==0&&(tt(i),i=[],r=[],s=[],l=0,o=[]),n.fallback&&(r=[wt],s[0]=xe(h=>(i[0]=h,n.fallback())),l=1),s;for(r[0]===wt&&(i[0](),i=[],r=[],s=[],l=0),a=0;a<u;a++)a<r.length&&r[a]!==c[a]?o[a](()=>c[a]):a>=r.length&&(s[a]=xe(d));for(;a<r.length;a++)i[a]();return l=o.length=i.length=u,r=c.slice(0),s=s.slice(0,l)});function d(h){i[a]=h;const[p,$]=U(c[a]);return o[a]=$,t(p,a)}}}function f(e,t){return re(()=>e(t||{}))}function Ue(){return!0}const xt={get(e,t,n){return t===de?n:e.get(t)},has(e,t){return t===de?!0:e.has(t)},set:Ue,deleteProperty:Ue,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:Ue,deleteProperty:Ue}},ownKeys(e){return e.keys()}};function ft(e){return(e=typeof e=="function"?e():e)?e:{}}function or(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function Y(...e){let t=!1;for(let o=0;o<e.length;o++){const l=e[o];t=t||!!l&&de in l,e[o]=typeof l=="function"?(t=!0,H(l)):l}if(un&&t)return new Proxy({get(o){for(let l=e.length-1;l>=0;l--){const a=ft(e[l])[o];if(a!==void 0)return a}},has(o){for(let l=e.length-1;l>=0;l--)if(o in ft(e[l]))return!0;return!1},keys(){const o=[];for(let l=0;l<e.length;l++)o.push(...Object.keys(ft(e[l])));return[...new Set(o)]}},xt);const n={},r=Object.create(null);for(let o=e.length-1;o>=0;o--){const l=e[o];if(!l)continue;const a=Object.getOwnPropertyNames(l);for(let c=a.length-1;c>=0;c--){const u=a[c];if(u==="__proto__"||u==="constructor")continue;const d=Object.getOwnPropertyDescriptor(l,u);if(!r[u])r[u]=d.get?{enumerable:!0,configurable:!0,get:or.bind(n[u]=[d.get.bind(l)])}:d.value!==void 0?d:void 0;else{const h=n[u];h&&(d.get?h.push(d.get.bind(l)):d.value!==void 0&&h.push(()=>d.value))}}}const s={},i=Object.keys(r);for(let o=i.length-1;o>=0;o--){const l=i[o],a=r[l];a&&a.get?Object.defineProperty(s,l,a):s[l]=a?a.value:void 0}return s}function Lt(e,...t){if(un&&de in e){const s=new Set(t.length>1?t.flat():t[0]),i=t.map(o=>new Proxy({get(l){return o.includes(l)?e[l]:void 0},has(l){return o.includes(l)&&l in e},keys(){return o.filter(l=>l in e)}},xt));return i.push(new Proxy({get(o){return s.has(o)?void 0:e[o]},has(o){return s.has(o)?!1:o in e},keys(){return Object.keys(e).filter(o=>!s.has(o))}},xt)),i}const n={},r=t.map(()=>({}));for(const s of Object.getOwnPropertyNames(e)){const i=Object.getOwnPropertyDescriptor(e,s),o=!i.get&&!i.set&&i.enumerable&&i.writable&&i.configurable;let l=!1,a=0;for(const c of t)c.includes(s)&&(l=!0,o?r[a][s]=i.value:Object.defineProperty(r[a],s,i)),++a;l||(o?n[s]=i.value:Object.defineProperty(n,s,i))}return[...r,n]}const lr=e=>`Stale read from <${e}>.`;function Nt(e){const t="fallback"in e&&{fallback:()=>e.fallback};return H(sr(()=>e.each,e.children,t||void 0))}function ar(e){const t="fallback"in e&&{fallback:()=>e.fallback};return H(ir(()=>e.each,e.children,t||void 0))}function L(e){const t=e.keyed,n=H(()=>e.when,void 0,void 0),r=t?n:H(n,void 0,{equals:(s,i)=>!s==!i});return H(()=>{const s=r();if(s){const i=e.children;return typeof i=="function"&&i.length>0?re(()=>i(t?s:()=>{if(!re(r))throw lr("Show");return n()})):i}return e.fallback},void 0,void 0)}const cr=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected"],ur=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline",...cr]),dr=new Set(["innerHTML","textContent","innerText","children"]),fr=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),hr=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1}});function gr(e,t){const n=hr[e];return typeof n=="object"?n[t]?n.$:void 0:n}const mr=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),pr=new Set(["altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","set","stop","svg","switch","symbol","text","textPath","tref","tspan","use","view","vkern"]),yr={xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace"},$e=e=>H(()=>e());function br(e,t,n){let r=n.length,s=t.length,i=r,o=0,l=0,a=t[s-1].nextSibling,c=null;for(;o<s||l<i;){if(t[o]===n[l]){o++,l++;continue}for(;t[s-1]===n[i-1];)s--,i--;if(s===o){const u=i<r?l?n[l-1].nextSibling:n[i-l]:a;for(;l<i;)e.insertBefore(n[l++],u)}else if(i===l)for(;o<s;)(!c||!c.has(t[o]))&&t[o].remove(),o++;else if(t[o]===n[i-1]&&n[l]===t[s-1]){const u=t[--s].nextSibling;e.insertBefore(n[l++],t[o++].nextSibling),e.insertBefore(n[--i],u),t[s]=n[i]}else{if(!c){c=new Map;let d=l;for(;d<i;)c.set(n[d],d++)}const u=c.get(t[o]);if(u!=null)if(l<u&&u<i){let d=o,h=1,p;for(;++d<s&&d<i&&!((p=c.get(t[d]))==null||p!==u+h);)h++;if(h>u-l){const $=t[o];for(;l<u;)e.insertBefore(n[l++],$)}else e.replaceChild(n[l++],t[o++])}else o++;else t[o++].remove()}}}const Bt="_$DX_DELEGATE";function vr(e,t,n,r={}){let s;return xe(i=>{s=i,t===document?e():b(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{s(),t.textContent=""}}function C(e,t,n,r){let s;const i=()=>{const l=document.createElement("template");return l.innerHTML=e,l.content.firstChild},o=()=>(s||(s=i())).cloneNode(!0);return o.cloneNode=o,o}function be(e,t=window.document){const n=t[Bt]||(t[Bt]=new Set);for(let r=0,s=e.length;r<s;r++){const i=e[r];n.has(i)||(n.add(i),t.addEventListener(i,Cr))}}function G(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function wr(e,t,n,r){r==null?e.removeAttributeNS(t,n):e.setAttributeNS(t,n,r)}function xr(e,t,n){n?e.setAttribute(t,""):e.removeAttribute(t)}function J(e,t){t==null?e.removeAttribute("class"):e.className=t}function ot(e,t,n,r){if(r)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const s=n[0];e.addEventListener(t,n[0]=i=>s.call(e,n[1],i))}else e.addEventListener(t,n,typeof n!="function"&&n)}function $r(e,t,n={}){const r=Object.keys(t||{}),s=Object.keys(n);let i,o;for(i=0,o=s.length;i<o;i++){const l=s[i];!l||l==="undefined"||t[l]||(Ht(e,l,!1),delete n[l])}for(i=0,o=r.length;i<o;i++){const l=r[i],a=!!t[l];!l||l==="undefined"||n[l]===a||!a||(Ht(e,l,!0),n[l]=a)}return n}function _r(e,t,n){if(!t)return n?G(e,"style"):t;const r=e.style;if(typeof t=="string")return r.cssText=t;typeof n=="string"&&(r.cssText=n=void 0),n||(n={}),t||(t={});let s,i;for(i in n)t[i]==null&&r.removeProperty(i),delete n[i];for(i in t)s=t[i],s!==n[i]&&(r.setProperty(i,s),n[i]=s);return n}function jt(e,t={},n,r){const s={};return r||N(()=>s.children=Le(e,t.children,s.children)),N(()=>typeof t.ref=="function"&&wn(t.ref,e)),N(()=>Sr(e,t,n,!0,s,!0)),s}function wn(e,t,n){return re(()=>e(t,n))}function b(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return Le(e,t,r,n);N(s=>Le(e,t(),s,n),r)}function Sr(e,t,n,r,s={},i=!1){t||(t={});for(const o in s)if(!(o in t)){if(o==="children")continue;s[o]=Vt(e,o,null,s[o],n,i,t)}for(const o in t){if(o==="children")continue;const l=t[o];s[o]=Vt(e,o,l,s[o],n,i,t)}}function kr(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function Ht(e,t,n){const r=t.trim().split(/\s+/);for(let s=0,i=r.length;s<i;s++)e.classList.toggle(r[s],n)}function Vt(e,t,n,r,s,i,o){let l,a,c,u,d;if(t==="style")return _r(e,n,r);if(t==="classList")return $r(e,n,r);if(n===r)return r;if(t==="ref")i||n(e);else if(t.slice(0,3)==="on:"){const h=t.slice(3);r&&e.removeEventListener(h,r,typeof r!="function"&&r),n&&e.addEventListener(h,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const h=t.slice(10);r&&e.removeEventListener(h,r,!0),n&&e.addEventListener(h,n,!0)}else if(t.slice(0,2)==="on"){const h=t.slice(2).toLowerCase(),p=mr.has(h);if(!p&&r){const $=Array.isArray(r)?r[0]:r;e.removeEventListener(h,$)}(p||n)&&(ot(e,h,n,p),p&&be([h]))}else if(t.slice(0,5)==="attr:")G(e,t.slice(5),n);else if(t.slice(0,5)==="bool:")xr(e,t.slice(5),n);else if((d=t.slice(0,5)==="prop:")||(c=dr.has(t))||!s&&((u=gr(t,e.tagName))||(a=ur.has(t)))||(l=e.nodeName.includes("-")||"is"in o))d&&(t=t.slice(5),a=!0),t==="class"||t==="className"?J(e,n):l&&!a&&!c?e[kr(t)]=n:e[u||t]=n;else{const h=s&&t.indexOf(":")>-1&&yr[t.split(":")[0]];h?wr(e,h,t,n):G(e,fr[t]||t,n)}return n}function Cr(e){let t=e.target;const n=`$$${e.type}`,r=e.target,s=e.currentTarget,i=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),o=()=>{const a=t[n];if(a&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?a.call(t,c,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&i(t.host),!0},l=()=>{for(;o()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const a=e.composedPath();i(a[0]);for(let c=0;c<a.length-2&&(t=a[c],!!o());c++){if(t._$host){t=t._$host,l();break}if(t.parentNode===s)break}}else l();i(r)}function Le(e,t,n,r,s){for(;typeof n=="function";)n=n();if(t===n)return n;const i=typeof t,o=r!==void 0;if(e=o&&n[0]&&n[0].parentNode||e,i==="string"||i==="number"){if(i==="number"&&(t=t.toString(),t===n))return n;if(o){let l=n[0];l&&l.nodeType===3?l.data!==t&&(l.data=t):l=document.createTextNode(t),n=Se(e,n,r,l)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||i==="boolean")n=Se(e,n,r);else{if(i==="function")return N(()=>{let l=t();for(;typeof l=="function";)l=l();n=Le(e,l,n,r)}),()=>n;if(Array.isArray(t)){const l=[],a=n&&Array.isArray(n);if($t(l,t,n,s))return N(()=>n=Le(e,l,n,r,!0)),()=>n;if(l.length===0){if(n=Se(e,n,r),o)return n}else a?n.length===0?Yt(e,l,r):br(e,n,l):(n&&Se(e),Yt(e,l));n=l}else if(t.nodeType){if(Array.isArray(n)){if(o)return n=Se(e,n,r,t);Se(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function $t(e,t,n,r){let s=!1;for(let i=0,o=t.length;i<o;i++){let l=t[i],a=n&&n[e.length],c;if(!(l==null||l===!0||l===!1))if((c=typeof l)=="object"&&l.nodeType)e.push(l);else if(Array.isArray(l))s=$t(e,l,a)||s;else if(c==="function")if(r){for(;typeof l=="function";)l=l();s=$t(e,Array.isArray(l)?l:[l],Array.isArray(a)?a:[a])||s}else e.push(l),s=!0;else{const u=String(l);a&&a.nodeType===3&&a.data===u?e.push(a):e.push(document.createTextNode(u))}}return s}function Yt(e,t,n=null){for(let r=0,s=t.length;r<s;r++)e.insertBefore(t[r],n)}function Se(e,t,n,r){if(n===void 0)return e.textContent="";const s=r||document.createTextNode("");if(t.length){let i=!1;for(let o=t.length-1;o>=0;o--){const l=t[o];if(s!==l){const a=l.parentNode===e;!i&&!o?a?e.replaceChild(s,l):e.insertBefore(s,n):a&&l.remove()}else i=!0}}else e.insertBefore(s,n);return[s]}const Ar=!1,Or="http://www.w3.org/2000/svg";function Mr(e,t=!1){return t?document.createElementNS(Or,e):document.createElement(e)}function Er(e,t){const n=H(e);return H(()=>{const r=n();switch(typeof r){case"function":return re(()=>r(t));case"string":const s=pr.has(r),i=Mr(r,s);return jt(i,t,s),i}})}function Tr(e){const[,t]=Lt(e,["component"]);return Er(()=>e.component,t)}function xn(){let e=new Set;function t(s){return e.add(s),()=>e.delete(s)}let n=!1;function r(s,i){if(n)return!(n=!1);const o={to:s,options:i,defaultPrevented:!1,preventDefault:()=>o.defaultPrevented=!0};for(const l of e)l.listener({...o,from:l.location,retry:a=>{a&&(n=!0),l.navigate(s,{...i,resolve:!1})}});return!o.defaultPrevented}return{subscribe:t,confirm:r}}let _t;function Ut(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),_t=window.history.state._depth}Ut();function Rr(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function Ir(e,t){let n=!1;return()=>{const r=_t;Ut();const s=r==null?null:_t-r;if(n){n=!1;return}s&&t(s)?(n=!0,window.history.go(-s)):e()}}const Pr=/^(?:[a-z0-9]+:)?\/\//i,Dr=/^\/+|(\/)\/+$/g,$n="http://sr";function _e(e,t=!1){const n=e.replace(Dr,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function Ye(e,t,n){if(Pr.test(t))return;const r=_e(e),s=n&&_e(n);let i="";return!s||t.startsWith("/")?i=r:s.toLowerCase().indexOf(r.toLowerCase())!==0?i=r+s:i=s,(i||"/")+_e(t,!i)}function Lr(e,t){if(e==null)throw new Error(t);return e}function Nr(e,t){return _e(e).replace(/\/*(\*.*)?$/g,"")+_e(t)}function _n(e){const t={};return e.searchParams.forEach((n,r)=>{r in t?Array.isArray(t[r])?t[r].push(n):t[r]=[t[r],n]:t[r]=n}),t}function jr(e,t,n){const[r,s]=e.split("/*",2),i=r.split("/").filter(Boolean),o=i.length;return l=>{const a=l.split("/").filter(Boolean),c=a.length-o;if(c<0||c>0&&s===void 0&&!t)return null;const u={path:o?"":"/",params:{}},d=h=>n===void 0?void 0:n[h];for(let h=0;h<o;h++){const p=i[h],$=p[0]===":",v=$?a[h]:a[h].toLowerCase(),_=$?p.slice(1):p.toLowerCase();if($&&ht(v,d(_)))u.params[_]=v;else if($||!ht(v,_))return null;u.path+=`/${v}`}if(s){const h=c?a.slice(-c).join("/"):"";if(ht(h,d(s)))u.params[s]=h;else return null}return u}}function ht(e,t){const n=r=>r===e;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function Ur(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((s,i)=>s+(i.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function Sn(e){const t=new Map,n=gn();return new Proxy({},{get(r,s){return t.has(s)||Rt(n,()=>t.set(s,H(()=>e()[s]))),t.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function kn(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const s=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)s.push(n+=t[1]),r=r.slice(t[0].length);return kn(r).reduce((i,o)=>[...i,...s.map(l=>l+o)],[])}const zr=100,Cn=mn(),zt=mn(),Ft=()=>Lr(It(Cn),"<A> and 'use' router primitives can be only used inside a Route."),Fr=()=>It(zt)||Ft().base,qr=e=>{const t=Fr();return H(()=>t.resolvePath(e()))},Wr=e=>{const t=Ft();return H(()=>{const n=e();return n!==void 0?t.renderPath(n):n})},Br=()=>Ft().location;function Hr(e,t=""){const{component:n,preload:r,load:s,children:i,info:o}=e,l=!i||Array.isArray(i)&&!i.length,a={key:e,component:n,preload:r||s,info:o};return An(e.path).reduce((c,u)=>{for(const d of kn(u)){const h=Nr(t,d);let p=l?h:h.split("/*",1)[0];p=p.split("/").map($=>$.startsWith(":")||$.startsWith("*")?$:encodeURIComponent($)).join("/"),c.push({...a,originalPath:u,pattern:p,matcher:jr(p,!l,e.matchFilters)})}return c},[])}function Vr(e,t=0){return{routes:e,score:Ur(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let s=e.length-1;s>=0;s--){const i=e[s],o=i.matcher(n);if(!o)return null;r.unshift({...o,route:i})}return r}}}function An(e){return Array.isArray(e)?e:[e]}function On(e,t="",n=[],r=[]){const s=An(e);for(let i=0,o=s.length;i<o;i++){const l=s[i];if(l&&typeof l=="object"){l.hasOwnProperty("path")||(l.path="");const a=Hr(l,t);for(const c of a){n.push(c);const u=Array.isArray(l.children)&&l.children.length===0;if(l.children&&!u)On(l.children,c.pattern,n,r);else{const d=Vr([...n],r.length);r.push(d)}n.pop()}}}return n.length?r:r.sort((i,o)=>o.score-i.score)}function gt(e,t){for(let n=0,r=e.length;n<r;n++){const s=e[n].matcher(t);if(s)return s}return[]}function Yr(e,t,n){const r=new URL($n),s=H(u=>{const d=e();try{return new URL(d,r)}catch{return console.error(`Invalid path ${d}`),u}},r,{equals:(u,d)=>u.href===d.href}),i=H(()=>s().pathname),o=H(()=>s().search,!0),l=H(()=>s().hash),a=()=>"",c=Et(o,()=>_n(s()));return{get pathname(){return i()},get search(){return o()},get hash(){return l()},get state(){return t()},get key(){return a()},query:n?n(c):Sn(c)}}let we;function Kr(){return we}function Gr(e,t,n,r={}){const{signal:[s,i],utils:o={}}=e,l=o.parsePath||(w=>w),a=o.renderPath||(w=>w),c=o.beforeLeave||xn(),u=Ye("",r.base||"");if(u===void 0)throw new Error(`${u} is not a valid base path`);u&&!s().value&&i({value:u,replace:!0,scroll:!1});const[d,h]=U(!1);let p;const $=(w,m)=>{m.value===v()&&m.state===O()||(p===void 0&&h(!0),we=w,p=m,Zn(()=>{p===m&&(_(p.value),k(p.state),F[1](y=>y.filter(S=>S.pending)))}).finally(()=>{p===m&&Oe(()=>{we=void 0,w==="navigate"&&W(p),h(!1),p=void 0})}))},[v,_]=U(s().value),[O,k]=U(s().state),D=Yr(v,O,o.queryWrapper),P=[],F=U([]),K=H(()=>typeof r.transformUrl=="function"?gt(t(),r.transformUrl(D.pathname)):gt(t(),D.pathname)),ae=()=>{const w=K(),m={};for(let y=0;y<w.length;y++)Object.assign(m,w[y].params);return m},ce=o.paramsWrapper?o.paramsWrapper(ae,t):Sn(ae),q={pattern:u,path:()=>u,outlet:()=>null,resolvePath(w){return Ye(u,w)}};return N(Et(s,w=>$("native",w),{defer:!0})),{base:q,location:D,params:ce,isRouting:d,renderPath:a,parsePath:l,navigatorFactory:I,matches:K,beforeLeave:c,preloadRoute:ee,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:F};function R(w,m,y){re(()=>{if(typeof m=="number"){m&&(o.go?o.go(m):console.warn("Router integration does not support relative routing"));return}const S=!m||m[0]==="?",{replace:x,resolve:M,scroll:g,state:A}={replace:!1,resolve:!S,scroll:!0,...y},E=M?w.resolvePath(m):Ye(S&&D.pathname||"",m);if(E===void 0)throw new Error(`Path '${m}' is not a routable path`);if(P.length>=zr)throw new Error("Too many redirects");const T=v();(E!==T||A!==O())&&(Ar||c.confirm(E,y)&&(P.push({value:T,replace:x,scroll:g,state:O()}),$("navigate",{value:E,state:A})))})}function I(w){return w=w||It(zt)||q,(m,y)=>R(w,m,y)}function W(w){const m=P[0];m&&(i({...w,replace:m.replace,scroll:m.scroll}),P.length=0)}function ee(w,m){const y=gt(t(),w.pathname),S=we;we="preload";for(let x in y){const{route:M,params:g}=y[x];M.component&&M.component.preload&&M.component.preload();const{preload:A}=M;m&&A&&Rt(n(),()=>A({params:g,location:{pathname:w.pathname,search:w.search,hash:w.hash,query:_n(w),state:null,key:""},intent:"preload"}))}we=S}}function Jr(e,t,n,r){const{base:s,location:i,params:o}=e,{pattern:l,component:a,preload:c}=r().route,u=H(()=>r().path);a&&a.preload&&a.preload();const d=c?c({params:o,location:i,intent:we||"initial"}):void 0;return{parent:t,pattern:l,path:u,outlet:()=>a?f(a,{params:o,location:i,data:d,get children(){return n()}}):n(),resolvePath(p){return Ye(s.path(),p,u())}}}const Xr=e=>t=>{const{base:n}=t,r=Pt(()=>t.children),s=H(()=>On(r(),t.base||""));let i;const o=Gr(e,s,()=>i,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(o),f(Cn.Provider,{value:o,get children(){return f(Zr,{routerState:o,get root(){return t.root},get preload(){return t.rootPreload||t.rootLoad},get children(){return[$e(()=>(i=gn())&&null),f(Qr,{routerState:o,get branches(){return s()}})]}})}})};function Zr(e){const t=e.routerState.location,n=e.routerState.params,r=H(()=>e.preload&&re(()=>{e.preload({params:n,location:t,intent:Kr()||"initial"})}));return f(L,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:s=>f(s,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function Qr(e){const t=[];let n;const r=H(Et(e.routerState.matches,(s,i,o)=>{let l=i&&s.length===i.length;const a=[];for(let c=0,u=s.length;c<u;c++){const d=i&&i[c],h=s[c];o&&d&&h.route.key===d.route.key?a[c]=o[c]:(l=!1,t[c]&&t[c](),xe(p=>{t[c]=p,a[c]=Jr(e.routerState,a[c-1]||e.routerState.base,Kt(()=>r()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(s.length).forEach(c=>c()),o&&l?o:(n=a[0],a)}));return Kt(()=>r()&&n)()}const Kt=e=>()=>f(L,{get when(){return e()},keyed:!0,children:t=>f(zt.Provider,{value:t,get children(){return t.outlet()}})}),ze=e=>{const t=Pt(()=>e.children);return Y(e,{get children(){return t()}})};function es([e,t],n,r){return[e,r?s=>t(r(s)):t]}function ts(e){let t=!1;const n=s=>typeof s=="string"?{value:s}:s,r=es(U(n(e.get()),{equals:(s,i)=>s.value===i.value&&s.state===i.state}),void 0,s=>(!t&&e.set(s),s));return e.init&&Te(e.init((s=e.get())=>{t=!0,r[1](n(s)),t=!1})),Xr({signal:r,create:e.create,utils:e.utils})}function ns(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function rs(e,t){const n=e&&document.getElementById(e);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const ss=new Map;function is(e=!0,t=!1,n="/_server",r){return s=>{const i=s.base.path(),o=s.navigatorFactory(s.base);let l,a;function c(v){return v.namespaceURI==="http://www.w3.org/2000/svg"}function u(v){if(v.defaultPrevented||v.button!==0||v.metaKey||v.altKey||v.ctrlKey||v.shiftKey)return;const _=v.composedPath().find(K=>K instanceof Node&&K.nodeName.toUpperCase()==="A");if(!_||t&&!_.hasAttribute("link"))return;const O=c(_),k=O?_.href.baseVal:_.href;if((O?_.target.baseVal:_.target)||!k&&!_.hasAttribute("state"))return;const P=(_.getAttribute("rel")||"").split(/\s+/);if(_.hasAttribute("download")||P&&P.includes("external"))return;const F=O?new URL(k,document.baseURI):new URL(k);if(!(F.origin!==window.location.origin||i&&F.pathname&&!F.pathname.toLowerCase().startsWith(i.toLowerCase())))return[_,F]}function d(v){const _=u(v);if(!_)return;const[O,k]=_,D=s.parsePath(k.pathname+k.search+k.hash),P=O.getAttribute("state");v.preventDefault(),o(D,{resolve:!1,replace:O.hasAttribute("replace"),scroll:!O.hasAttribute("noscroll"),state:P?JSON.parse(P):void 0})}function h(v){const _=u(v);if(!_)return;const[O,k]=_;r&&(k.pathname=r(k.pathname)),s.preloadRoute(k,O.getAttribute("preload")!=="false")}function p(v){clearTimeout(l);const _=u(v);if(!_)return a=null;const[O,k]=_;a!==O&&(r&&(k.pathname=r(k.pathname)),l=setTimeout(()=>{s.preloadRoute(k,O.getAttribute("preload")!=="false"),a=O},20))}function $(v){if(v.defaultPrevented)return;let _=v.submitter&&v.submitter.hasAttribute("formaction")?v.submitter.getAttribute("formaction"):v.target.getAttribute("action");if(!_)return;if(!_.startsWith("https://action/")){const k=new URL(_,$n);if(_=s.parsePath(k.pathname+k.search),!_.startsWith(n))return}if(v.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const O=ss.get(_);if(O){v.preventDefault();const k=new FormData(v.target,v.submitter);O.call({r:s,f:v.target},v.target.enctype==="multipart/form-data"?k:new URLSearchParams(k))}}be(["click","submit"]),document.addEventListener("click",d),e&&(document.addEventListener("mousemove",p,{passive:!0}),document.addEventListener("focusin",h,{passive:!0}),document.addEventListener("touchstart",h,{passive:!0})),document.addEventListener("submit",$),Te(()=>{document.removeEventListener("click",d),e&&(document.removeEventListener("mousemove",p),document.removeEventListener("focusin",h),document.removeEventListener("touchstart",h)),document.removeEventListener("submit",$)})}}function os(e){const t=()=>{const r=window.location.pathname.replace(/^\/+/,"/")+window.location.search,s=window.history.state&&window.history.state._depth&&Object.keys(window.history.state).length===1?void 0:window.history.state;return{value:r+window.location.hash,state:s}},n=xn();return ts({get:t,set({value:r,replace:s,scroll:i,state:o}){s?window.history.replaceState(Rr(o),"",r):window.history.pushState(o,"",r),rs(decodeURIComponent(window.location.hash.slice(1)),i),Ut()},init:r=>ns(window,"popstate",Ir(r,s=>{if(s&&s<0)return!n.confirm(s);{const i=t();return!n.confirm(i.value,{state:i.state})}})),create:is(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}var ls=C("<a>");function Mn(e){e=Y({inactiveClass:"inactive",activeClass:"active"},e);const[,t]=Lt(e,["href","state","class","activeClass","inactiveClass","end"]),n=qr(()=>e.href),r=Wr(n),s=Br(),i=H(()=>{const o=n();if(o===void 0)return[!1,!1];const l=_e(o.split(/[?#]/,1)[0]).toLowerCase(),a=decodeURI(_e(s.pathname).toLowerCase());return[e.end?l===a:a.startsWith(l+"/")||a===l,l===a]});return(()=>{var o=ls();return jt(o,Y(t,{get href(){return r()||e.href},get state(){return JSON.stringify(e.state)},get classList(){return{...e.class&&{[e.class]:!0},[e.inactiveClass]:!i()[0],[e.activeClass]:i()[0],...t.classList}},link:"",get"aria-current"(){return i()[1]?"page":void 0}}),!1,!1),o})()}const St=Symbol("store-raw"),Me=Symbol("store-node"),me=Symbol("store-has"),En=Symbol("store-self");function Tn(e){let t=e[de];if(!t&&(Object.defineProperty(e,de,{value:t=new Proxy(e,us)}),!Array.isArray(e))){const n=Object.keys(e),r=Object.getOwnPropertyDescriptors(e);for(let s=0,i=n.length;s<i;s++){const o=n[s];r[o].get&&Object.defineProperty(e,o,{enumerable:r[o].enumerable,get:r[o].get.bind(t)})}}return t}function ve(e){let t;return e!=null&&typeof e=="object"&&(e[de]||!(t=Object.getPrototypeOf(e))||t===Object.prototype||Array.isArray(e))}function Ee(e,t=new Set){let n,r,s,i;if(n=e!=null&&e[St])return n;if(!ve(e)||t.has(e))return e;if(Array.isArray(e)){Object.isFrozen(e)?e=e.slice(0):t.add(e);for(let o=0,l=e.length;o<l;o++)s=e[o],(r=Ee(s,t))!==s&&(e[o]=r)}else{Object.isFrozen(e)?e=Object.assign({},e):t.add(e);const o=Object.keys(e),l=Object.getOwnPropertyDescriptors(e);for(let a=0,c=o.length;a<c;a++)i=o[a],!l[i].get&&(s=e[i],(r=Ee(s,t))!==s&&(e[i]=r))}return e}function nt(e,t){let n=e[t];return n||Object.defineProperty(e,t,{value:n=Object.create(null)}),n}function Ne(e,t,n){if(e[t])return e[t];const[r,s]=U(n,{equals:!1,internal:!0});return r.$=s,e[t]=r}function as(e,t){const n=Reflect.getOwnPropertyDescriptor(e,t);return!n||n.get||!n.configurable||t===de||t===Me||(delete n.value,delete n.writable,n.get=()=>e[de][t]),n}function Rn(e){bt()&&Ne(nt(e,Me),En)()}function cs(e){return Rn(e),Reflect.ownKeys(e)}const us={get(e,t,n){if(t===St)return e;if(t===de)return n;if(t===Je)return Rn(e),n;const r=nt(e,Me),s=r[t];let i=s?s():e[t];if(t===Me||t===me||t==="__proto__")return i;if(!s){const o=Object.getOwnPropertyDescriptor(e,t);bt()&&(typeof i!="function"||e.hasOwnProperty(t))&&!(o&&o.get)&&(i=Ne(r,t,i)())}return ve(i)?Tn(i):i},has(e,t){return t===St||t===de||t===Je||t===Me||t===me||t==="__proto__"?!0:(bt()&&Ne(nt(e,me),t)(),t in e)},set(){return!0},deleteProperty(){return!0},ownKeys:cs,getOwnPropertyDescriptor:as};function le(e,t,n,r=!1){if(!r&&e[t]===n)return;const s=e[t],i=e.length;n===void 0?(delete e[t],e[me]&&e[me][t]&&s!==void 0&&e[me][t].$()):(e[t]=n,e[me]&&e[me][t]&&s===void 0&&e[me][t].$());let o=nt(e,Me),l;if((l=Ne(o,t,s))&&l.$(()=>n),Array.isArray(e)&&e.length!==i){for(let a=e.length;a<i;a++)(l=o[a])&&l.$();(l=Ne(o,"length",i))&&l.$(e.length)}(l=o[En])&&l.$()}function In(e,t){const n=Object.keys(t);for(let r=0;r<n.length;r+=1){const s=n[r];le(e,s,t[s])}}function ds(e,t){if(typeof t=="function"&&(t=t(e)),t=Ee(t),Array.isArray(t)){if(e===t)return;let n=0,r=t.length;for(;n<r;n++){const s=t[n];e[n]!==s&&le(e,n,s)}le(e,"length",r)}else In(e,t)}function Ie(e,t,n=[]){let r,s=e;if(t.length>1){r=t.shift();const o=typeof r,l=Array.isArray(e);if(Array.isArray(r)){for(let a=0;a<r.length;a++)Ie(e,[r[a]].concat(t),n);return}else if(l&&o==="function"){for(let a=0;a<e.length;a++)r(e[a],a)&&Ie(e,[a].concat(t),n);return}else if(l&&o==="object"){const{from:a=0,to:c=e.length-1,by:u=1}=r;for(let d=a;d<=c;d+=u)Ie(e,[d].concat(t),n);return}else if(t.length>1){Ie(e[r],t,[r].concat(n));return}s=e[r],n=[r].concat(n)}let i=t[0];typeof i=="function"&&(i=i(s,n),i===s)||r===void 0&&i==null||(i=Ee(i),r===void 0||ve(s)&&ve(i)&&!Array.isArray(i)?In(s,i):le(e,r,i))}function fs(...[e,t]){const n=Ee(e||{}),r=Array.isArray(n),s=Tn(n);function i(...o){Oe(()=>{r&&o.length===1?ds(n,o[0]):Ie(n,o)})}return[s,i]}const kt=Symbol("store-root");function Ae(e,t,n,r,s){const i=t[n];if(e===i)return;const o=Array.isArray(e);if(n!==kt&&(!ve(e)||!ve(i)||o!==Array.isArray(i)||s&&e[s]!==i[s])){le(t,n,e);return}if(o){if(e.length&&i.length&&(!r||s&&e[0]&&e[0][s]!=null)){let c,u,d,h,p,$,v,_;for(d=0,h=Math.min(i.length,e.length);d<h&&(i[d]===e[d]||s&&i[d]&&e[d]&&i[d][s]===e[d][s]);d++)Ae(e[d],i,d,r,s);const O=new Array(e.length),k=new Map;for(h=i.length-1,p=e.length-1;h>=d&&p>=d&&(i[h]===e[p]||s&&i[h]&&e[p]&&i[h][s]===e[p][s]);h--,p--)O[p]=i[h];if(d>p||d>h){for(u=d;u<=p;u++)le(i,u,e[u]);for(;u<e.length;u++)le(i,u,O[u]),Ae(e[u],i,u,r,s);i.length>e.length&&le(i,"length",e.length);return}for(v=new Array(p+1),u=p;u>=d;u--)$=e[u],_=s&&$?$[s]:$,c=k.get(_),v[u]=c===void 0?-1:c,k.set(_,u);for(c=d;c<=h;c++)$=i[c],_=s&&$?$[s]:$,u=k.get(_),u!==void 0&&u!==-1&&(O[u]=i[c],u=v[u],k.set(_,u));for(u=d;u<e.length;u++)u in O?(le(i,u,O[u]),Ae(e[u],i,u,r,s)):le(i,u,e[u])}else for(let c=0,u=e.length;c<u;c++)Ae(e[c],i,c,r,s);i.length>e.length&&le(i,"length",e.length);return}const l=Object.keys(e);for(let c=0,u=l.length;c<u;c++)Ae(e[l[c]],i,l[c],r,s);const a=Object.keys(i);for(let c=0,u=a.length;c<u;c++)e[a[c]]===void 0&&le(i,a[c],void 0)}function hs(e,t={}){const{merge:n,key:r="id"}=t,s=Ee(e);return i=>{if(!ve(i)||!ve(s))return s;const o=Ae(s,{[kt]:i},kt,n,r);return o===void 0?i:o}}const gs="/dev-assets/portraitmode-logo-BM2pkTKm.png";/**
* @license lucide-solid v0.514.0 - ISC
*
* This source code is licensed under the ISC license.
* See the LICENSE file in the root directory of this source tree.
*/var ms={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"},ke=ms,ps=C("<svg>"),Gt=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ys=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),bs=e=>{const t=ys(e);return t.charAt(0).toUpperCase()+t.slice(1)},vs=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),ws=e=>{const[t,n]=Lt(e,["color","size","strokeWidth","children","class","name","iconNode","absoluteStrokeWidth"]);return(()=>{var r=ps();return jt(r,Y(ke,{get width(){return t.size??ke.width},get height(){return t.size??ke.height},get stroke(){return t.color??ke.stroke},get"stroke-width"(){return $e(()=>!!t.absoluteStrokeWidth)()?Number(t.strokeWidth??ke["stroke-width"])*24/Number(t.size):Number(t.strokeWidth??ke["stroke-width"])},get class(){return vs("lucide","lucide-icon",...t.name!=null?[`lucide-${Gt(bs(t.name))}`,`lucide-${Gt(t.name)}`]:[],t.class!=null?t.class:"")}},n),!0,!0),b(r,f(Nt,{get each(){return t.iconNode},children:([s,i])=>f(Tr,Y({component:s},i))})),r})()},Q=ws,xs=[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]],$s=e=>f(Q,Y(e,{iconNode:xs,name:"camera"})),_s=$s,Ss=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],ks=e=>f(Q,Y(e,{iconNode:Ss,name:"circle-help"})),Cs=ks,As=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],Os=e=>f(Q,Y(e,{iconNode:As,name:"database"})),Ms=Os,Es=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Ts=e=>f(Q,Y(e,{iconNode:Es,name:"file-text"})),Rs=Ts,Is=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],Ps=e=>f(Q,Y(e,{iconNode:Is,name:"heart"})),Ds=Ps,Ls=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ns=e=>f(Q,Y(e,{iconNode:Ls,name:"house"})),Pn=Ns,js=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],Us=e=>f(Q,Y(e,{iconNode:js,name:"loader-circle"})),zs=Us,Fs=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],qs=e=>f(Q,Y(e,{iconNode:Fs,name:"lock"})),Ws=qs,Bs=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Hs=e=>f(Q,Y(e,{iconNode:Bs,name:"menu"})),Vs=Hs,Ys=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],Ks=e=>f(Q,Y(e,{iconNode:Ys,name:"message-circle"})),Gs=Ks,Js=[["circle",{cx:"9",cy:"12",r:"3",key:"u3jwor"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]],Xs=e=>f(Q,Y(e,{iconNode:Js,name:"toggle-left"})),Zs=Xs,Qs=[["circle",{cx:"15",cy:"12",r:"3",key:"1afu0r"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]],ei=e=>f(Q,Y(e,{iconNode:Qs,name:"toggle-right"})),ti=ei,ni=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],ri=e=>f(Q,Y(e,{iconNode:ni,name:"trash"})),Dn=ri,si=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],ii=e=>f(Q,Y(e,{iconNode:si,name:"user-check"})),Ln=ii,oi=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],li=e=>f(Q,Y(e,{iconNode:oi,name:"user-plus"})),ai=li,ci=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],ui=e=>f(Q,Y(e,{iconNode:ci,name:"user-x"})),qt=ui,di=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],fi=e=>f(Q,Y(e,{iconNode:di,name:"x"})),hi=fi;const ge={ajaxUrl:window.pmAjaxUrl??"https://portraitmode.io/wp-admin/admin-ajax.php",siteUrl:window.pmSiteUrl??"https://portraitmode.io"};var Jt=C("<label>"),gi=C('<div><div class="relative inline-block"><input type=checkbox class=sr-only><label><div><span>'),Nn=C("<div>"),Xt=C('<span class="ml-2 inline-block">'),jn=C("<a>"),Zt=C('<span class="bottom-navbar-tab inline-block text-xs">'),mi=C('<section><div class="bottom-navbar-tabs container flex w-full justify-between">');function Fe(e){const t=e.id||`toggle-${Math.random().toString(36).substring(2,9)}`;function n(r){const s=r.target instanceof HTMLInputElement&&r.target.checked;e.onChange?.(s)}return(()=>{var r=gi(),s=r.firstChild,i=s.firstChild,o=i.nextSibling,l=o.firstChild,a=l.firstChild;return b(r,f(L,{get when(){return e.label&&e.labelPosition==="start"},get children(){var c=Jt();return G(c,"for",t),b(c,()=>e.label),N(()=>J(c,`mr-2 inline-flex text-sm font-medium ${e.disabled?"text-gray-400":"text-gray-700"}`)),c}}),s),i.addEventListener("change",n),G(i,"id",t),G(o,"for",t),b(r,f(L,{get when(){return e.label&&e.labelPosition!=="start"},get children(){var c=Jt();return G(c,"for",t),b(c,()=>e.label),N(()=>J(c,`ml-2 inline-flex text-sm font-medium ${e.disabled?"text-gray-400":"text-gray-700"}`)),c}}),null),N(c=>{var u=`flex items-center ${e.class??""}`,d=e.disabled??!1,h=`flex cursor-pointer items-center ${e.disabled?"cursor-not-allowed opacity-50":""} `,p=`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${e.checked?"bg-blue-600":"bg-gray-200"} ${e.disabled?"":"hover:bg-opacity-90"} focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2`,$=`inline-block h-4 w-4 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out ${e.checked?"translate-x-6":"translate-x-1"} `;return u!==c.e&&J(r,c.e=u),d!==c.t&&(i.disabled=c.t=d),h!==c.a&&J(o,c.a=h),p!==c.o&&J(l,c.o=p),$!==c.i&&J(a,c.i=$),c},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),N(()=>i.checked=e.checked??!1),r})()}function pi(e){const t=e.type??"info";let n=`notice rounded-md border-2 p-2 text-sm font-semibold ${e.class??""}`;return t==="info"?n+=" bg-info-bg text-info border-info":t==="success"?n+=" bg-success-bg text-success border-success":t==="warning"?n+=" bg-warning-bg text-warning border-warning":(t==="error"||t==="failed")&&(n+=" bg-error-bg text-error border-error"),(()=>{var r=Nn();return J(r,n),b(r,()=>e.message),r})()}function Ct(e){return(()=>{var t=Nn();return b(t,f(zs,{get class(){return`mx-auto ${e.sizeClass??"h-10 w-10"} animate-spin`}})),N(()=>J(t,`w-full text-center ${e.colorClass??"text-pmBaseColor"} ${e.class??""}`)),t})()}function qe(e){const t="flex cursor-pointer px-4 py-2 transition-colors hover:rounded-md hover:bg-stone-100 hover:text-pmAccentColor rounded-xs";return f(L,{get when(){return e.reload},get fallback(){return f(Mn,{get href(){return e.href},get class(){return`${t} ${e.class??""}`},get activeClass(){return e.noActiveClass?void 0:"bg-pmLightColor text-pmAccentColor"},get onClick(){return e.onClick},get children(){return[f(L,{get when(){return e.icon},get children(){return e.icon}}),(()=>{var n=Xt();return b(n,()=>e.text),n})()]}})},get children(){var n=jn();return ot(n,"click",e.onClick,!0),b(n,f(L,{get when(){return e.icon},get children(){return e.icon}}),null),b(n,f(L,{get when(){return e.text},get children(){var r=Xt();return b(r,()=>e.text),r}}),null),N(r=>{var s=e.href,i=`${t} ${e.class??""}`;return s!==r.e&&G(n,"href",r.e=s),i!==r.t&&J(n,r.t=i),r},{e:void 0,t:void 0}),n}})}function We(e){return f(L,{get when(){return e.reload},get fallback(){return f(Mn,{get href(){return e.href},get class(){return`hover:text-pmAccentColor focus:text-pmAccentColor block text-center ${e.class??""}`},get activeClass(){return e.noActiveClass?void 0:"text-pmAccentColor"},get onClick(){return e.onClick},get children(){return[f(L,{get when(){return e.icon},get children(){return e.icon}}),f(L,{get when(){return e.text},get children(){var t=Zt();return b(t,()=>e.text),t}})]}})},get children(){var t=jn();return ot(t,"click",e.onClick,!0),b(t,f(L,{get when(){return e.icon},get children(){return e.icon}}),null),b(t,f(L,{get when(){return e.text},get children(){var n=Zt();return b(n,()=>e.text),n}}),null),N(n=>{var r=e.href,s=`hover:text-pmAccentColor focus:text-pmAccentColor block text-center ${e.class??""}`;return r!==n.e&&G(t,"href",n.e=r),s!==n.t&&J(t,n.t=s),n},{e:void 0,t:void 0}),t}})}function yi(e){return(()=>{var t=mi(),n=t.firstChild;return b(n,f(We,{get href(){return ge.siteUrl},get icon(){return f(Pn,{class:"mx-auto h-5 w-5"})},text:"Website",reload:!0}),null),b(n,f(We,{href:"/dev/pro-members",get icon(){return f(Ln,{class:"mx-auto h-5 w-5"})},text:"Pro Members"}),null),b(n,f(We,{href:"/dev/inactive-users",get icon(){return f(qt,{class:"mx-auto h-5 w-5"})},text:"Inactive Users"}),null),b(n,f(We,{href:"#mobile-menu",get icon(){return f(Vs,{class:"mx-auto h-5 w-5"})},text:"Menu",get onClick(){return e.onHamburgerMenuClick},noActiveClass:!0}),null),N(()=>J(t,`bottom-navbar ${e.class??""} bg-pmBaseColorAlt fixed bottom-0 left-0 z-9997 block w-screen overflow-x-scroll border-t border-t-stone-200 px-4 pt-2 pb-1 sm:px-8 lg:hidden`)),t})()}be(["click"]);class Qt extends Error{response;request;options;constructor(t,n,r){const s=t.status||t.status===0?t.status:"",i=t.statusText||"",o=`${s} ${i}`.trim(),l=o?`status code ${o}`:"an unknown error";super(`Request failed with ${l}: ${n.method} ${n.url}`),this.name="HTTPError",this.response=t,this.request=n,this.options=r}}class Un extends Error{request;constructor(t){super(`Request timed out: ${t.method} ${t.url}`),this.name="TimeoutError",this.request=t}}const en=(()=>{let e=!1,t=!1;const n=typeof globalThis.ReadableStream=="function",r=typeof globalThis.Request=="function";if(n&&r)try{t=new globalThis.Request("https://empty.invalid",{body:new globalThis.ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type")}catch(s){if(s instanceof Error&&s.message==="unsupported BodyInit type")return!1;throw s}return e&&!t})(),bi=typeof globalThis.AbortController=="function",vi=typeof globalThis.ReadableStream=="function",wi=typeof globalThis.FormData=="function",zn=["get","post","put","patch","head","delete"],xi={json:"application/json",text:"text/*",formData:"multipart/form-data",arrayBuffer:"*/*",blob:"*/*"},mt=2147483647,$i=new TextEncoder().encode("------WebKitFormBoundaryaxpyiPgbbPti10Rw").length,Fn=Symbol("stop"),_i={json:!0,parseJson:!0,stringifyJson:!0,searchParams:!0,prefixUrl:!0,retry:!0,timeout:!0,hooks:!0,throwHttpErrors:!0,onDownloadProgress:!0,onUploadProgress:!0,fetch:!0},Si={method:!0,headers:!0,body:!0,mode:!0,credentials:!0,cache:!0,redirect:!0,referrer:!0,referrerPolicy:!0,integrity:!0,keepalive:!0,signal:!0,window:!0,dispatcher:!0,duplex:!0,priority:!0},ki=e=>{if(!e)return 0;if(e instanceof FormData){let t=0;for(const[n,r]of e)t+=$i,t+=new TextEncoder().encode(`Content-Disposition: form-data; name="${n}"`).length,t+=typeof r=="string"?new TextEncoder().encode(r).length:r.size;return t}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength;if(typeof e=="string")return new TextEncoder().encode(e).length;if(e instanceof URLSearchParams)return new TextEncoder().encode(e.toString()).length;if("byteLength"in e)return e.byteLength;if(typeof e=="object"&&e!==null)try{const t=JSON.stringify(e);return new TextEncoder().encode(t).length}catch{return 0}return 0},Ci=(e,t)=>{const n=Number(e.headers.get("content-length"))||0;let r=0;return e.status===204?(t&&t({percent:1,totalBytes:n,transferredBytes:r},new Uint8Array),new Response(null,{status:e.status,statusText:e.statusText,headers:e.headers})):new Response(new ReadableStream({async start(s){const i=e.body.getReader();t&&t({percent:0,transferredBytes:0,totalBytes:n},new Uint8Array);async function o(){const{done:l,value:a}=await i.read();if(l){s.close();return}if(t){r+=a.byteLength;const c=n===0?0:r/n;t({percent:c,transferredBytes:r,totalBytes:n},a)}s.enqueue(a),await o()}await o()}}),{status:e.status,statusText:e.statusText,headers:e.headers})},Ai=(e,t)=>{const n=ki(e.body);let r=0;return new Request(e,{duplex:"half",body:new ReadableStream({async start(s){const i=e.body instanceof ReadableStream?e.body.getReader():new Response("").body.getReader();async function o(){const{done:l,value:a}=await i.read();if(l){t&&t({percent:1,transferredBytes:r,totalBytes:Math.max(n,r)},new Uint8Array),s.close();return}r+=a.byteLength;let c=n===0?0:r/n;(n<r||c===1)&&(c=.99),t&&t({percent:Number(c.toFixed(2)),transferredBytes:r,totalBytes:n},a),s.enqueue(a),await o()}await o()}})})},Pe=e=>e!==null&&typeof e=="object",Be=(...e)=>{for(const t of e)if((!Pe(t)||Array.isArray(t))&&t!==void 0)throw new TypeError("The `options` argument must be an object");return Wt({},...e)},qn=(e={},t={})=>{const n=new globalThis.Headers(e),r=t instanceof globalThis.Headers,s=new globalThis.Headers(t);for(const[i,o]of s.entries())r&&o==="undefined"||o===void 0?n.delete(i):n.set(i,o);return n};function He(e,t,n){return Object.hasOwn(t,n)&&t[n]===void 0?[]:Wt(e[n]??[],t[n]??[])}const Wn=(e={},t={})=>({beforeRequest:He(e,t,"beforeRequest"),beforeRetry:He(e,t,"beforeRetry"),afterResponse:He(e,t,"afterResponse"),beforeError:He(e,t,"beforeError")}),Wt=(...e)=>{let t={},n={},r={};for(const s of e)if(Array.isArray(s))Array.isArray(t)||(t=[]),t=[...t,...s];else if(Pe(s)){for(let[i,o]of Object.entries(s))Pe(o)&&i in t&&(o=Wt(t[i],o)),t={...t,[i]:o};Pe(s.hooks)&&(r=Wn(r,s.hooks),t.hooks=r),Pe(s.headers)&&(n=qn(n,s.headers),t.headers=n)}return t},Oi=e=>zn.includes(e)?e.toUpperCase():e,Mi=["get","put","head","delete","options","trace"],Ei=[408,413,429,500,502,503,504],Ti=[413,429,503],tn={limit:2,methods:Mi,statusCodes:Ei,afterStatusCodes:Ti,maxRetryAfter:Number.POSITIVE_INFINITY,backoffLimit:Number.POSITIVE_INFINITY,delay:e=>.3*2**(e-1)*1e3},Ri=(e={})=>{if(typeof e=="number")return{...tn,limit:e};if(e.methods&&!Array.isArray(e.methods))throw new Error("retry.methods must be an array");if(e.statusCodes&&!Array.isArray(e.statusCodes))throw new Error("retry.statusCodes must be an array");return{...tn,...e}};async function Ii(e,t,n,r){return new Promise((s,i)=>{const o=setTimeout(()=>{n&&n.abort(),i(new Un(e))},r.timeout);r.fetch(e,t).then(s).catch(i).then(()=>{clearTimeout(o)})})}async function Pi(e,{signal:t}){return new Promise((n,r)=>{t&&(t.throwIfAborted(),t.addEventListener("abort",s,{once:!0}));function s(){clearTimeout(i),r(t.reason)}const i=setTimeout(()=>{t?.removeEventListener("abort",s),n()},e)})}const Di=(e,t)=>{const n={};for(const r in t)!(r in Si)&&!(r in _i)&&!(r in e)&&(n[r]=t[r]);return n};class rt{static create(t,n){const r=new rt(t,n),s=async()=>{if(typeof r._options.timeout=="number"&&r._options.timeout>mt)throw new RangeError(`The \`timeout\` option cannot be greater than ${mt}`);await Promise.resolve();let l=await r._fetch();for(const a of r._options.hooks.afterResponse){const c=await a(r.request,r._options,r._decorateResponse(l.clone()));c instanceof globalThis.Response&&(l=c)}if(r._decorateResponse(l),!l.ok&&r._options.throwHttpErrors){let a=new Qt(l,r.request,r._options);for(const c of r._options.hooks.beforeError)a=await c(a);throw a}if(r._options.onDownloadProgress){if(typeof r._options.onDownloadProgress!="function")throw new TypeError("The `onDownloadProgress` option must be a function");if(!vi)throw new Error("Streams are not supported in your environment. `ReadableStream` is missing.");return Ci(l.clone(),r._options.onDownloadProgress)}return l},o=(r._options.retry.methods.includes(r.request.method.toLowerCase())?r._retry(s):s()).finally(async()=>{r.request.bodyUsed||await r.request.body?.cancel()});for(const[l,a]of Object.entries(xi))o[l]=async()=>{r.request.headers.set("accept",r.request.headers.get("accept")||a);const c=await o;if(l==="json"){if(c.status===204||(await c.clone().arrayBuffer()).byteLength===0)return"";if(n.parseJson)return n.parseJson(await c.text())}return c[l]()};return o}request;abortController;_retryCount=0;_input;_options;constructor(t,n={}){if(this._input=t,this._options={...n,headers:qn(this._input.headers,n.headers),hooks:Wn({beforeRequest:[],beforeRetry:[],beforeError:[],afterResponse:[]},n.hooks),method:Oi(n.method??this._input.method??"GET"),prefixUrl:String(n.prefixUrl||""),retry:Ri(n.retry),throwHttpErrors:n.throwHttpErrors!==!1,timeout:n.timeout??1e4,fetch:n.fetch??globalThis.fetch.bind(globalThis)},typeof this._input!="string"&&!(this._input instanceof URL||this._input instanceof globalThis.Request))throw new TypeError("`input` must be a string, URL, or Request");if(this._options.prefixUrl&&typeof this._input=="string"){if(this._input.startsWith("/"))throw new Error("`input` must not begin with a slash when using `prefixUrl`");this._options.prefixUrl.endsWith("/")||(this._options.prefixUrl+="/"),this._input=this._options.prefixUrl+this._input}if(bi){const r=this._options.signal??this._input.signal;this.abortController=new globalThis.AbortController,this._options.signal=r?AbortSignal.any([r,this.abortController.signal]):this.abortController.signal}if(en&&(this._options.duplex="half"),this._options.json!==void 0&&(this._options.body=this._options.stringifyJson?.(this._options.json)??JSON.stringify(this._options.json),this._options.headers.set("content-type",this._options.headers.get("content-type")??"application/json")),this.request=new globalThis.Request(this._input,this._options),this._options.searchParams){const s="?"+(typeof this._options.searchParams=="string"?this._options.searchParams.replace(/^\?/,""):new URLSearchParams(this._options.searchParams).toString()),i=this.request.url.replace(/(?:\?.*?)?(?=#|$)/,s);(wi&&this._options.body instanceof globalThis.FormData||this._options.body instanceof URLSearchParams)&&!(this._options.headers&&this._options.headers["content-type"])&&this.request.headers.delete("content-type"),this.request=new globalThis.Request(new globalThis.Request(i,{...this.request}),this._options)}if(this._options.onUploadProgress){if(typeof this._options.onUploadProgress!="function")throw new TypeError("The `onUploadProgress` option must be a function");if(!en)throw new Error("Request streams are not supported in your environment. The `duplex` option for `Request` is not available.");this.request.body&&(this.request=Ai(this.request,this._options.onUploadProgress))}}_calculateRetryDelay(t){if(this._retryCount++,this._retryCount>this._options.retry.limit||t instanceof Un)throw t;if(t instanceof Qt){if(!this._options.retry.statusCodes.includes(t.response.status))throw t;const r=t.response.headers.get("Retry-After")??t.response.headers.get("RateLimit-Reset")??t.response.headers.get("X-RateLimit-Reset")??t.response.headers.get("X-Rate-Limit-Reset");if(r&&this._options.retry.afterStatusCodes.includes(t.response.status)){let s=Number(r)*1e3;Number.isNaN(s)?s=Date.parse(r)-Date.now():s>=Date.parse("2024-01-01")&&(s-=Date.now());const i=this._options.retry.maxRetryAfter??s;return s<i?s:i}if(t.response.status===413)throw t}const n=this._options.retry.delay(this._retryCount);return Math.min(this._options.retry.backoffLimit,n)}_decorateResponse(t){return this._options.parseJson&&(t.json=async()=>this._options.parseJson(await t.text())),t}async _retry(t){try{return await t()}catch(n){const r=Math.min(this._calculateRetryDelay(n),mt);if(this._retryCount<1)throw n;await Pi(r,{signal:this._options.signal});for(const s of this._options.hooks.beforeRetry)if(await s({request:this.request,options:this._options,error:n,retryCount:this._retryCount})===Fn)return;return this._retry(t)}}async _fetch(){for(const r of this._options.hooks.beforeRequest){const s=await r(this.request,this._options);if(s instanceof Request){this.request=s;break}if(s instanceof Response)return s}const t=Di(this.request,this._options),n=this.request;return this.request=n.clone(),this._options.timeout===!1?this._options.fetch(n,t):Ii(n,t,this.abortController,this._options)}}/*! MIT License © Sindre Sorhus */const At=e=>{const t=(n,r)=>rt.create(n,Be(e,r));for(const n of zn)t[n]=(r,s)=>rt.create(r,Be(e,s,{method:n}));return t.create=n=>At(Be(n)),t.extend=n=>(typeof n=="function"&&(n=n(e??{})),At(Be(e,n))),t.stop=Fn,t},lt=At();function je(e){if(typeof e=="string")return e;if(e.message)return e.message;if(e.response&&e.response.message)return e.response.message;const t="An error occurred. Please try again later.";if(!e.errors||!e.errors.length)return t;const n=e.errors[0];return n.detail?n.detail:n.title?n.title:t}async function Li(){try{return await lt.get(ge.ajaxUrl,{searchParams:{action:"pm_fetch_pro_members_with_extra_data"}}).json()}catch(e){return console.log("error:",e),{success:!1,message:je(e)}}}var Ni=C('<header class="mb-4 flex w-full flex-wrap items-center justify-between border-b border-stone-300 px-4 pb-4 md:px-0"><div class="w-full sm:w-9/12 md:w-10/12"><h1 class="mb-2 text-2xl font-semibold">Pro Members</h1><p class=text-sm></p></div><div class="mt-3 flex w-full items-center sm:mt-0 sm:w-3/12 sm:justify-end md:w-2/12"><button class="inline-flex items-center"><span class="ml-2 text-sm font-medium">Has issues'),ji=C('<tr><td colspan=5 class="py-4 text-center">'),Ui=C('<div class="relative overflow-x-auto lg:w-full"><table class="relative w-full min-w-[708px] text-sm"><thead class=text-left><tr class=bg-pmLightColor><th class="w-1/12 rounded-l-md px-4 py-2 font-medium">ID</th><th class="w-4/12 px-4 py-2 font-medium">Display name</th><th class="w-4/12 px-4 py-2 font-medium">Email</th><th class="w-1/12 px-4 py-2 font-medium">Remaining token</th><th class="w-1/12 rounded-r-md px-4 py-2 font-medium">Used token</th></tr></thead><tbody>'),zi=C('<tr><td class="px-4 py-2.5 text-xs font-semibold"></td><td class="px-4 py-2.5"><a class=text-pmAccentColor></a></td><td class="px-4 py-2.5"></td><td class="px-4 py-2.5"></td><td class="px-4 py-2.5">');function pt(){const[e]=Xn(Li),[t,n]=U(!1);function r(){n(!t())}function s(o){return!o||!o.length?[]:o.filter(l=>{const c=l.remainingFeedbackTokens,u=l.totalFeedbackRequests;let d=!1;const h=3;if(c===0)(u===0||u<10-h)&&(d=!0);else{const p=10-u;c<p-h&&(d=!0)}return d})}function i(o){return!o||!o.length?0:t()?s(o).length:o.length}return[(()=>{var o=Ni(),l=o.firstChild,a=l.firstChild,c=a.nextSibling,u=l.nextSibling,d=u.firstChild,h=d.firstChild;return b(c,(()=>{var p=$e(()=>!!e.loading);return()=>p()?"Fetching pro members with active subscriptions...":`Showing ${i(e()?.data)} pro members with active subscriptions${t()?" and might have feedback token issue":""}`})()),d.$$click=r,b(d,f(L,{get when(){return t()},get fallback(){return f(Zs,{class:"h-8 w-8"})},get children(){return f(ti,{class:"text-pmAccentColor h-8 w-8"})}}),h),o})(),(()=>{var o=Ui(),l=o.firstChild,a=l.firstChild,c=a.nextSibling;return b(c,f(L,{get when(){return e.loading},get children(){var u=ji(),d=u.firstChild;return b(d,f(Ct,{})),u}}),null),b(c,f(Nt,{get each(){return $e(()=>!!e()?.data)()?$e(()=>!!t())()?s(e()?.data):e()?.data:[]},children:(u,d)=>(()=>{var h=zi(),p=h.firstChild,$=p.nextSibling,v=$.firstChild,_=$.nextSibling,O=_.nextSibling,k=O.nextSibling;return b(p,()=>u.id),b(v,()=>u.displayName),b(_,()=>u.email),b(O,()=>u.remainingFeedbackTokens),b(k,()=>u.totalFeedbackRequests),N(D=>{var P=`hover:bg-stone-100 ${d()===0?"":"border-pmBaseColor border-t"}`,F=`${ge.siteUrl}/profile/${u.nicename}`;return P!==D.e&&J(h,D.e=P),F!==D.t&&G(v,"href",D.t=F),D},{e:void 0,t:void 0}),h})()}),null),o})()]}be(["click"]);function Ce(e,t,n){let r=n.initialDeps??[],s;function i(){var o,l,a,c;let u;n.key&&((o=n.debug)!=null&&o.call(n))&&(u=Date.now());const d=e();if(!(d.length!==r.length||d.some(($,v)=>r[v]!==$)))return s;r=d;let p;if(n.key&&((l=n.debug)!=null&&l.call(n))&&(p=Date.now()),s=t(...d),n.key&&((a=n.debug)!=null&&a.call(n))){const $=Math.round((Date.now()-u)*100)/100,v=Math.round((Date.now()-p)*100)/100,_=v/16,O=(k,D)=>{for(k=String(k);k.length<D;)k=" "+k;return k};console.info(`%c⏱ ${O(v,5)} /${O($,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*_,120))}deg 100% 31%);`,n?.key)}return(c=n?.onChange)==null||c.call(n,s),s}return i.updateDeps=o=>{r=o},i}function nn(e,t){if(e===void 0)throw new Error("Unexpected undefined");return e}const Fi=(e,t)=>Math.abs(e-t)<=1,qi=(e,t,n)=>{let r;return function(...s){e.clearTimeout(r),r=e.setTimeout(()=>t.apply(this,s),n)}},Wi=e=>e,Bi=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),r=[];for(let s=t;s<=n;s++)r.push(s);return r},Ot={passive:!0},Hi=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=()=>{t({width:n.innerWidth,height:n.innerHeight})};return r(),n.addEventListener("resize",r,Ot),()=>{n.removeEventListener("resize",r)}},rn=typeof window>"u"?!0:"onscrollend"in window,Vi=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;let s=0;const i=e.options.useScrollendEvent&&rn?()=>{}:qi(r,()=>{t(s,!1)},e.options.isScrollingResetDelay),o=u=>()=>{s=n[e.options.horizontal?"scrollX":"scrollY"],i(),t(s,u)},l=o(!0),a=o(!1);a(),n.addEventListener("scroll",l,Ot);const c=e.options.useScrollendEvent&&rn;return c&&n.addEventListener("scrollend",a,Ot),()=>{n.removeEventListener("scroll",l),c&&n.removeEventListener("scrollend",a)}},Yi=(e,t,n)=>{if(t?.borderBoxSize){const r=t.borderBoxSize[0];if(r)return Math.round(r[n.options.horizontal?"inlineSize":"blockSize"])}return e[n.options.horizontal?"offsetWidth":"offsetHeight"]},Ki=(e,{adjustments:t=0,behavior:n},r)=>{var s,i;const o=e+t;(i=(s=r.scrollElement)==null?void 0:s.scrollTo)==null||i.call(s,{[r.options.horizontal?"left":"top"]:o,behavior:n})};class Gi{constructor(t){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let n=null;const r=()=>n||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:n=new this.targetWindow.ResizeObserver(s=>{s.forEach(i=>{const o=()=>{this._measureElement(i.target,i)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(o):o()})}));return{disconnect:()=>{var s;(s=r())==null||s.disconnect(),n=null},observe:s=>{var i;return(i=r())==null?void 0:i.observe(s,{box:"border-box"})},unobserve:s=>{var i;return(i=r())==null?void 0:i.unobserve(s)}}})(),this.range=null,this.setOptions=n=>{Object.entries(n).forEach(([r,s])=>{typeof s>"u"&&delete n[r]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Wi,rangeExtractor:Bi,onChange:()=>{},measureElement:Yi,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...n}},this.notify=n=>{var r,s;(s=(r=this.options).onChange)==null||s.call(r,this,n)},this.maybeNotify=Ce(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),n=>{this.notify(n)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(n=>n()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var n;const r=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==r){if(this.cleanup(),!r){this.maybeNotify();return}this.scrollElement=r,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=((n=this.scrollElement)==null?void 0:n.window)??null,this.elementsCache.forEach(s=>{this.observer.observe(s)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,s=>{this.scrollRect=s,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(s,i)=>{this.scrollAdjustments=0,this.scrollDirection=i?this.getScrollOffset()<s?"forward":"backward":null,this.scrollOffset=s,this.isScrolling=i,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??(typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(n,r)=>{const s=new Map,i=new Map;for(let o=r-1;o>=0;o--){const l=n[o];if(s.has(l.lane))continue;const a=i.get(l.lane);if(a==null||l.end>a.end?i.set(l.lane,l):l.end<a.end&&s.set(l.lane,!0),s.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((o,l)=>o.end===l.end?o.index-l.index:o.end-l.end)[0]:void 0},this.getMeasurementOptions=Ce(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(n,r,s,i,o)=>(this.pendingMeasuredCacheIndexes=[],{count:n,paddingStart:r,scrollMargin:s,getItemKey:i,enabled:o}),{key:!1}),this.getMeasurements=Ce(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:n,paddingStart:r,scrollMargin:s,getItemKey:i,enabled:o},l)=>{if(!o)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(u=>{this.itemSizeCache.set(u.key,u.size)}));const a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const c=this.measurementsCache.slice(0,a);for(let u=a;u<n;u++){const d=i(u),h=this.options.lanes===1?c[u-1]:this.getFurthestMeasurement(c,u),p=h?h.end+this.options.gap:r+s,$=l.get(d),v=typeof $=="number"?$:this.options.estimateSize(u),_=p+v,O=h?h.lane:u%this.options.lanes;c[u]={index:u,start:p,size:v,end:_,key:d,lane:O}}return this.measurementsCache=c,c},{key:!1,debug:()=>this.options.debug}),this.calculateRange=Ce(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(n,r,s,i)=>this.range=n.length>0&&r>0?Ji({measurements:n,outerSize:r,scrollOffset:s,lanes:i}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=Ce(()=>{let n=null,r=null;const s=this.calculateRange();return s&&(n=s.startIndex,r=s.endIndex),this.maybeNotify.updateDeps([this.isScrolling,n,r]),[this.options.rangeExtractor,this.options.overscan,this.options.count,n,r]},(n,r,s,i,o)=>i===null||o===null?[]:n({startIndex:i,endIndex:o,overscan:r,count:s}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=n=>{const r=this.options.indexAttribute,s=n.getAttribute(r);return s?parseInt(s,10):(console.warn(`Missing attribute name '${r}={index}' on measured element.`),-1)},this._measureElement=(n,r)=>{const s=this.indexFromElement(n),i=this.measurementsCache[s];if(!i)return;const o=i.key,l=this.elementsCache.get(o);l!==n&&(l&&this.observer.unobserve(l),this.observer.observe(n),this.elementsCache.set(o,n)),n.isConnected&&this.resizeItem(s,this.options.measureElement(n,r,this))},this.resizeItem=(n,r)=>{const s=this.measurementsCache[n];if(!s)return;const i=this.itemSizeCache.get(s.key)??s.size,o=r-i;o!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(s,o,this):this.scrollDirection==="backward"&&s.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=o,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(s.index),this.itemSizeCache=new Map(this.itemSizeCache.set(s.key,r)),this.notify(!1))},this.measureElement=n=>{if(!n){this.elementsCache.forEach((r,s)=>{r.isConnected||(this.observer.unobserve(r),this.elementsCache.delete(s))});return}this._measureElement(n,void 0)},this.getVirtualItems=Ce(()=>[this.getVirtualIndexes(),this.getMeasurements()],(n,r)=>{const s=[];for(let i=0,o=n.length;i<o;i++){const l=n[i],a=r[l];s.push(a)}return s},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=n=>{const r=this.getMeasurements();if(r.length!==0)return nn(r[Bn(0,r.length-1,s=>nn(r[s]).start,n)])},this.getOffsetForAlignment=(n,r,s=0)=>{const i=this.getSize(),o=this.getScrollOffset();r==="auto"&&(r=n>=o+i?"end":"start"),r==="center"?n+=(s-i)/2:r==="end"&&(n-=i);const l=this.getTotalSize()-i;return Math.max(Math.min(l,n),0)},this.getOffsetForIndex=(n,r="auto")=>{n=Math.max(0,Math.min(n,this.options.count-1));const s=this.measurementsCache[n];if(!s)return;const i=this.getSize(),o=this.getScrollOffset();if(r==="auto")if(s.end>=o+i-this.options.scrollPaddingEnd)r="end";else if(s.start<=o+this.options.scrollPaddingStart)r="start";else return[o,r];const l=r==="end"?s.end+this.options.scrollPaddingEnd:s.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(l,r,s.size),r]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(n,{align:r="start",behavior:s}={})=>{this.cancelScrollToIndex(),s==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(n,r),{adjustments:void 0,behavior:s})},this.scrollToIndex=(n,{align:r="auto",behavior:s}={})=>{n=Math.max(0,Math.min(n,this.options.count-1)),this.cancelScrollToIndex(),s==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const i=this.getOffsetForIndex(n,r);if(!i)return;const[o,l]=i;this._scrollToOffset(o,{adjustments:void 0,behavior:s}),s!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(n))){const c=this.getOffsetForIndex(n,l);if(!c)return;const[u]=c,d=this.getScrollOffset();Fi(u,d)||this.scrollToIndex(n,{align:l,behavior:s})}else this.scrollToIndex(n,{align:l,behavior:s})}))},this.scrollBy=(n,{behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+n,{adjustments:void 0,behavior:r})},this.getTotalSize=()=>{var n;const r=this.getMeasurements();let s;if(r.length===0)s=this.options.paddingStart;else if(this.options.lanes===1)s=((n=r[r.length-1])==null?void 0:n.end)??0;else{const i=Array(this.options.lanes).fill(null);let o=r.length-1;for(;o>=0&&i.some(l=>l===null);){const l=r[o];i[l.lane]===null&&(i[l.lane]=l.end),o--}s=Math.max(...i.filter(l=>l!==null))}return Math.max(s-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(n,{adjustments:r,behavior:s})=>{this.options.scrollToFn(n,{behavior:s,adjustments:r},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(t)}}const Bn=(e,t,n,r)=>{for(;e<=t;){const s=(e+t)/2|0,i=n(s);if(i<r)e=s+1;else if(i>r)t=s-1;else return s}return e>0?e-1:0};function Ji({measurements:e,outerSize:t,scrollOffset:n,lanes:r}){const s=e.length-1,i=a=>e[a].start;if(e.length<=r)return{startIndex:0,endIndex:s};let o=Bn(0,s,i,n),l=o;if(r===1)for(;l<s&&e[l].end<n+t;)l++;else if(r>1){const a=Array(r).fill(0);for(;l<s&&a.some(u=>u<n+t);){const u=e[l];a[u.lane]=u.end,l++}const c=Array(r).fill(n+t);for(;o>=0&&c.some(u=>u>=n);){const u=e[o];c[u.lane]=u.start,o--}o=Math.max(0,o-o%r),l=Math.min(s,l+(r-1-l%r))}return{startIndex:o,endIndex:l}}function Xi(e){const t=Y(e),n=new Gi(t),[r,s]=fs(n.getVirtualItems()),[i,o]=U(n.getTotalSize()),l={get(c,u){switch(u){case"getVirtualItems":return()=>r;case"getTotalSize":return()=>i();default:return Reflect.get(c,u)}}},a=new Proxy(n,l);return a.setOptions(t),Tt(()=>{const c=a._didMount();a._willUpdate(),Te(c)}),hn(()=>{a.setOptions(Y(t,e,{onChange:(c,u)=>{var d;c._willUpdate(),s(hs(c.getVirtualItems(),{key:"index"})),o(c.getTotalSize()),(d=e.onChange)==null||d.call(e,c,u)}})),a.measure()}),a}function Zi(e){return Xi(Y({getScrollElement:()=>typeof document<"u"?window:null,observeElementRect:Hi,observeElementOffset:Vi,scrollToFn:Ki,initialOffset:()=>typeof document<"u"?window.scrollY:0},e))}async function Qi(){try{return await lt.get(ge.ajaxUrl,{searchParams:{action:"pm_fetch_inactive_users"}}).json()}catch(e){return console.log("error:",e),{success:!1,message:je(e)}}}async function eo(e){try{return await lt.post(ge.ajaxUrl,{body:new URLSearchParams({action:"pm_delete_inactive_user",user_id:e.toString()})}).json()}catch(t){return console.log("error:",t),{success:!1,message:je(t)}}}async function to(e){try{return await lt.post(ge.ajaxUrl,{body:new URLSearchParams({action:"pm_delete_inactive_users",user_ids:JSON.stringify(e)})}).json()}catch(t){return console.log("error:",t),{success:!1,message:je(t)}}}var no=C('<div class="mr-5 flex items-center"><span class="mr-2 font-medium">Max last session</span><input type=datetime-local class="h-8 rounded-md border border-stone-300 px-1.5 text-sm">'),ro=C('<div><div class="session-filter my-1.5 flex items-center"></div><div class="dots-filter my-1.5 mr-5 inline-flex items-center">Email contains at least<input type=number class="mx-2 inline-flex h-6 max-w-16 rounded-sm border border-stone-300 px-2 text-sm">dots');function so(e){return(()=>{var t=ro(),n=t.firstChild,r=n.nextSibling,s=r.firstChild,i=s.nextSibling;return b(t,f(Fe,{label:"Unverified",get checked(){return e.enableUnverified},class:"my-1.5 mr-5",get onChange(){return e.onUnverifiedFilterChange}}),n),b(n,f(Fe,{label:"Not logged-in",get checked(){return e.enableNotLoggedIn},class:"mr-5",get onChange(){return e.onNotLoggedInFilterChange}}),null),b(n,f(L,{get when(){return e.enableNotLoggedIn},get children(){var o=no(),l=o.firstChild,a=l.nextSibling;return a.addEventListener("change",c=>e.onMaxLastSessionFilterChange(c.currentTarget.value)),N(()=>a.value=e.maxLastSession??""),o}}),null),i.$$input=o=>e.onMinDotsFilterChange(o.currentTarget?.value?Number(o.currentTarget.value):void 0),b(t,f(Fe,{label:"Without IG",get checked(){return e.withoutIg},class:"my-1.5 mr-5",get onChange(){return e.onWithoutIgFilterChange}}),null),b(t,f(Fe,{label:"Without avatar",get checked(){return e.withoutAvatar},class:"my-1.5 mr-5",get onChange(){return e.onWithoutAvatarFilterChange}}),null),N(()=>J(t,`flex w-full flex-wrap items-center justify-start text-sm ${e.class}`)),N(()=>i.value=e.minDots??""),t})()}be(["input"]);var io=C('<div class="vt-td break-all text-wrap truncate text-sm inline-flex w-[3.5%] items-center px-1 text-center"><input type=checkbox name=select_all>'),oo=C('<div class="vt-td break-all text-wrap truncate text-sm w-[4.5%] px-1 text-xs font-semibold"><a class=text-pmAccentColor target=_blank>'),lo=C('<div class="vt-td break-all text-wrap truncate text-sm w-[10%] px-2"><time class=inline-block>'),sn=C("<time class=inline-block>"),on=C('<div class="vt-td break-all text-wrap truncate text-sm w-[10%] px-2">'),ao=C('<span class="font-semibold text-green-700">Yes'),co=C('<div class="vt-td break-all text-wrap truncate text-sm w-[7%] px-2 text-center">'),uo=C('<div class="vt-td break-all text-wrap truncate text-sm w-[13%] px-2"><a class=text-pmAccentColor>'),fo=C('<div class="vt-td break-all text-wrap truncate text-sm w-[17%] px-2">'),ho=C("<a class=text-pmAccentColor>@"),go=C('<div class="vt-td break-all text-wrap truncate text-sm w-[12%] px-2">'),mo=C("<div>"),po=C('<div class="vt-td break-all text-wrap truncate text-sm w-[7%] px-2 text-center"><button type=button class="delete-button inline-flex items-center">'),yo=C("<span class=text-red-600>No");function bo(e){const t="vt-td break-all text-wrap truncate text-sm";return[(()=>{var n=io(),r=n.firstChild;return r.addEventListener("change",()=>{e.onCheckboxClick?.(e.user.id)}),N(()=>r.checked=e.selected),n})(),(()=>{var n=oo(),r=n.firstChild;return b(r,()=>e.user.id),N(()=>G(r,"href",`https://portraitmode.io/wp-admin/user-edit.php?user_id=${e.user.id}`)),n})(),(()=>{var n=lo(),r=n.firstChild;return b(r,()=>e.user.formattedRegisteredDate),N(()=>G(r,"datetime",e.user.registeredDate)),n})(),(()=>{var n=on();return b(n,f(L,{get when(){return e.user.sessionStartDate},get children(){var r=sn();return b(r,()=>e.user.formattedSessionStartDate),N(()=>G(r,"datetime",e.user.sessionStartDate??"")),r}})),n})(),(()=>{var n=on();return b(n,f(L,{get when(){return e.user.sessionExpiryDate},get children(){var r=sn();return b(r,()=>e.user.formattedSessionExpiryDate),N(()=>G(r,"datetime",e.user.sessionExpiryDate??"")),r}})),n})(),(()=>{var n=co();return b(n,f(L,{get when(){return e.user.hasActiveSession},get fallback(){return yo()},get children(){return ao()}})),n})(),(()=>{var n=uo(),r=n.firstChild;return b(r,()=>e.user.displayName),N(()=>G(r,"href",`${ge.siteUrl}/profile/${e.user.nicename}`)),n})(),(()=>{var n=fo();return b(n,()=>e.user.email),n})(),(()=>{var n=go();return b(n,f(L,{get when(){return e.user.instagram},fallback:" ",get children(){var r=ho();return r.firstChild,b(r,()=>e.user.instagram,null),N(()=>G(r,"href",`https://instagram.com/${e.user.instagram}`)),r}})),n})(),(()=>{var n=mo();return b(n,()=>e.user.verified?"Yes":"No"),N(()=>J(n,`${t} w-[6%] px-2 text-center ${e.user.verified?"font-semibold text-green-700":"text-red-600"}`)),n})(),(()=>{var n=po(),r=n.firstChild;return r.$$click=()=>e.onDeleteButtonClick?.(e.user),b(r,f(Dn,{class:"h-5 w-5"})),n})()]}be(["click"]);var vo=C("<span>Fetching all inactive users..."),wo=C('<p class="mb-2 text-sm">'),xo=C('<code class="ml-2 inline-flex rounded-sm bg-slate-200 px-1.5 py-1">unverified'),$o=C('<code class="ml-2 inline-flex rounded-sm bg-slate-200 px-1.5 py-1">not logged-in'),_o=C('<code class="ml-2 inline-flex rounded-sm bg-slate-200 px-1.5 py-1">max last session'),So=C('<code class="ml-2 inline-flex rounded-sm bg-slate-200 px-1.5 py-1">min dots'),ko=C('<code class="ml-2 inline-flex rounded-sm bg-slate-200 px-1.5 py-1">without IG'),Co=C('<code class="ml-2 inline-flex rounded-sm bg-slate-200 px-1.5 py-1">without avatar');function Ao(e){return(()=>{var t=wo();return b(t,f(L,{get when(){return e.loading},get fallback(){return["Showing ",$e(()=>e.totalInactiveUsers)," inactive users"," ",$e(()=>e.hasActiveFilter?"with filters applied:":""),f(L,{get when(){return e.showOnlyUnverified},get children(){return xo()}}),f(L,{get when(){return e.showOnlyNotLoggedIn},get children(){return $o()}}),f(L,{get when(){return e.showOnlyWithMaxLastSession},get children(){return _o()}}),f(L,{get when(){return e.showOnlyWithMinDots},get children(){return So()}}),f(L,{get when(){return e.showOnlyWithoutIg},get children(){return ko()}}),f(L,{get when(){return e.showOnlyWithoutAvatar},get children(){return Co()}})]},get children(){return vo()}})),t})()}function Oo(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ke={exports:{}},Mo=Ke.exports,ln;function Eo(){return ln||(ln=1,function(e,t){(function(n,r){e.exports=r()})(Mo,function(){var n=1e3,r=6e4,s=36e5,i="millisecond",o="second",l="minute",a="hour",c="day",u="week",d="month",h="quarter",p="year",$="date",v="Invalid Date",_=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,O=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,k={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(w){var m=["th","st","nd","rd"],y=w%100;return"["+w+(m[(y-20)%10]||m[y]||m[0])+"]"}},D=function(w,m,y){var S=String(w);return!S||S.length>=m?w:""+Array(m+1-S.length).join(y)+w},P={s:D,z:function(w){var m=-w.utcOffset(),y=Math.abs(m),S=Math.floor(y/60),x=y%60;return(m<=0?"+":"-")+D(S,2,"0")+":"+D(x,2,"0")},m:function w(m,y){if(m.date()<y.date())return-w(y,m);var S=12*(y.year()-m.year())+(y.month()-m.month()),x=m.clone().add(S,d),M=y-x<0,g=m.clone().add(S+(M?-1:1),d);return+(-(S+(y-x)/(M?x-g:g-x))||0)},a:function(w){return w<0?Math.ceil(w)||0:Math.floor(w)},p:function(w){return{M:d,y:p,w:u,d:c,D:$,h:a,m:l,s:o,ms:i,Q:h}[w]||String(w||"").toLowerCase().replace(/s$/,"")},u:function(w){return w===void 0}},F="en",K={};K[F]=k;var ae="$isDayjsObject",ce=function(w){return w instanceof W||!(!w||!w[ae])},q=function w(m,y,S){var x;if(!m)return F;if(typeof m=="string"){var M=m.toLowerCase();K[M]&&(x=M),y&&(K[M]=y,x=M);var g=m.split("-");if(!x&&g.length>1)return w(g[0])}else{var A=m.name;K[A]=m,x=A}return!S&&x&&(F=x),x||!S&&F},R=function(w,m){if(ce(w))return w.clone();var y=typeof m=="object"?m:{};return y.date=w,y.args=arguments,new W(y)},I=P;I.l=q,I.i=ce,I.w=function(w,m){return R(w,{locale:m.$L,utc:m.$u,x:m.$x,$offset:m.$offset})};var W=function(){function w(y){this.$L=q(y.locale,null,!0),this.parse(y),this.$x=this.$x||y.x||{},this[ae]=!0}var m=w.prototype;return m.parse=function(y){this.$d=function(S){var x=S.date,M=S.utc;if(x===null)return new Date(NaN);if(I.u(x))return new Date;if(x instanceof Date)return new Date(x);if(typeof x=="string"&&!/Z$/i.test(x)){var g=x.match(_);if(g){var A=g[2]-1||0,E=(g[7]||"0").substring(0,3);return M?new Date(Date.UTC(g[1],A,g[3]||1,g[4]||0,g[5]||0,g[6]||0,E)):new Date(g[1],A,g[3]||1,g[4]||0,g[5]||0,g[6]||0,E)}}return new Date(x)}(y),this.init()},m.init=function(){var y=this.$d;this.$y=y.getFullYear(),this.$M=y.getMonth(),this.$D=y.getDate(),this.$W=y.getDay(),this.$H=y.getHours(),this.$m=y.getMinutes(),this.$s=y.getSeconds(),this.$ms=y.getMilliseconds()},m.$utils=function(){return I},m.isValid=function(){return this.$d.toString()!==v},m.isSame=function(y,S){var x=R(y);return this.startOf(S)<=x&&x<=this.endOf(S)},m.isAfter=function(y,S){return R(y)<this.startOf(S)},m.isBefore=function(y,S){return this.endOf(S)<R(y)},m.$g=function(y,S,x){return I.u(y)?this[S]:this.set(x,y)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(y,S){var x=this,M=!!I.u(S)||S,g=I.p(y),A=function(X,Z){var ue=I.w(x.$u?Date.UTC(x.$y,Z,X):new Date(x.$y,Z,X),x);return M?ue:ue.endOf(c)},E=function(X,Z){return I.w(x.toDate()[X].apply(x.toDate("s"),(M?[0,0,0,0]:[23,59,59,999]).slice(Z)),x)},T=this.$W,B=this.$M,V=this.$D,te="set"+(this.$u?"UTC":"");switch(g){case p:return M?A(1,0):A(31,11);case d:return M?A(1,B):A(0,B+1);case u:var ie=this.$locale().weekStart||0,se=(T<ie?T+7:T)-ie;return A(M?V-se:V+(6-se),B);case c:case $:return E(te+"Hours",0);case a:return E(te+"Minutes",1);case l:return E(te+"Seconds",2);case o:return E(te+"Milliseconds",3);default:return this.clone()}},m.endOf=function(y){return this.startOf(y,!1)},m.$set=function(y,S){var x,M=I.p(y),g="set"+(this.$u?"UTC":""),A=(x={},x[c]=g+"Date",x[$]=g+"Date",x[d]=g+"Month",x[p]=g+"FullYear",x[a]=g+"Hours",x[l]=g+"Minutes",x[o]=g+"Seconds",x[i]=g+"Milliseconds",x)[M],E=M===c?this.$D+(S-this.$W):S;if(M===d||M===p){var T=this.clone().set($,1);T.$d[A](E),T.init(),this.$d=T.set($,Math.min(this.$D,T.daysInMonth())).$d}else A&&this.$d[A](E);return this.init(),this},m.set=function(y,S){return this.clone().$set(y,S)},m.get=function(y){return this[I.p(y)]()},m.add=function(y,S){var x,M=this;y=Number(y);var g=I.p(S),A=function(B){var V=R(M);return I.w(V.date(V.date()+Math.round(B*y)),M)};if(g===d)return this.set(d,this.$M+y);if(g===p)return this.set(p,this.$y+y);if(g===c)return A(1);if(g===u)return A(7);var E=(x={},x[l]=r,x[a]=s,x[o]=n,x)[g]||1,T=this.$d.getTime()+y*E;return I.w(T,this)},m.subtract=function(y,S){return this.add(-1*y,S)},m.format=function(y){var S=this,x=this.$locale();if(!this.isValid())return x.invalidDate||v;var M=y||"YYYY-MM-DDTHH:mm:ssZ",g=I.z(this),A=this.$H,E=this.$m,T=this.$M,B=x.weekdays,V=x.months,te=x.meridiem,ie=function(Z,ue,oe,fe){return Z&&(Z[ue]||Z(S,M))||oe[ue].slice(0,fe)},se=function(Z){return I.s(A%12||12,Z,"0")},X=te||function(Z,ue,oe){var fe=Z<12?"AM":"PM";return oe?fe.toLowerCase():fe};return M.replace(O,function(Z,ue){return ue||function(oe){switch(oe){case"YY":return String(S.$y).slice(-2);case"YYYY":return I.s(S.$y,4,"0");case"M":return T+1;case"MM":return I.s(T+1,2,"0");case"MMM":return ie(x.monthsShort,T,V,3);case"MMMM":return ie(V,T);case"D":return S.$D;case"DD":return I.s(S.$D,2,"0");case"d":return String(S.$W);case"dd":return ie(x.weekdaysMin,S.$W,B,2);case"ddd":return ie(x.weekdaysShort,S.$W,B,3);case"dddd":return B[S.$W];case"H":return String(A);case"HH":return I.s(A,2,"0");case"h":return se(1);case"hh":return se(2);case"a":return X(A,E,!0);case"A":return X(A,E,!1);case"m":return String(E);case"mm":return I.s(E,2,"0");case"s":return String(S.$s);case"ss":return I.s(S.$s,2,"0");case"SSS":return I.s(S.$ms,3,"0");case"Z":return g}return null}(Z)||g.replace(":","")})},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(y,S,x){var M,g=this,A=I.p(S),E=R(y),T=(E.utcOffset()-this.utcOffset())*r,B=this-E,V=function(){return I.m(g,E)};switch(A){case p:M=V()/12;break;case d:M=V();break;case h:M=V()/3;break;case u:M=(B-T)/6048e5;break;case c:M=(B-T)/864e5;break;case a:M=B/s;break;case l:M=B/r;break;case o:M=B/n;break;default:M=B}return x?M:I.a(M)},m.daysInMonth=function(){return this.endOf(d).$D},m.$locale=function(){return K[this.$L]},m.locale=function(y,S){if(!y)return this.$L;var x=this.clone(),M=q(y,S,!0);return M&&(x.$L=M),x},m.clone=function(){return I.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},w}(),ee=W.prototype;return R.prototype=ee,[["$ms",i],["$s",o],["$m",l],["$H",a],["$W",c],["$M",d],["$y",p],["$D",$]].forEach(function(w){ee[w[1]]=function(m){return this.$g(m,w[0],w[1])}}),R.extend=function(w,m){return w.$i||(w(m,W,R),w.$i=!0),R},R.locale=q,R.isDayjs=ce,R.unix=function(w){return R(1e3*w)},R.en=K[F],R.Ls=K,R.p={},R})}(Ke)),Ke.exports}var To=Eo();const yt=Oo(To);function Ro(e){return{...e,formattedRegisteredDate:yt(e.registeredDate).format("MMM DD, YYYY"),instagram:e.instagram?.replace(/^@/,""),formattedSessionStartDate:e.sessionStartDate?yt(e.sessionStartDate).format("MMM DD, YYYY"):void 0,formattedSessionExpiryDate:e.sessionExpiryDate?yt(e.sessionExpiryDate).format("MMM DD, YYYY"):void 0,profileUrl:`${ge.siteUrl}/profile/${e.nicename}`}}const Ge=new Map,Hn=e=>{if(e.key==="Escape")for(const t of Ge.values())t(e)};let Mt=!1;const Io=()=>{Mt||(document.addEventListener("keydown",Hn),Mt=!0)},Po=e=>{if(!e)return;const t=Object.freeze({});Tt(()=>{Io(),Ge.set(t,e)}),Te(()=>{Ge.delete(t),Ge.size===0&&(document.removeEventListener("keydown",Hn),Mt=!1)})};var Do=C('<div class="dialog-backdrop data-[expanded]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[expanded]:fade-in-0 pointer-events-auto fixed inset-0 z-[9999] bg-[#ffffffcc] backdrop-blur-xs"aria-hidden=true>'),Lo=C('<h2 id=alert-dialog-title class="text-lg font-semibold">'),No=C('<p id=alert-dialog-description class="text-muted-foreground text-sm">'),jo=C("<footer>"),Uo=C('<button type=button aria-label="Close dialog"class="ring-offset-background data-[expanded]:bg-accent data-[expanded]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none"><span class=sr-only>Close'),zo=C('<div tabindex=-1 id=alert-dialog-content role=alertdialog aria-modal=true class="dialog alert-dialog data-[expanded]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[expanded]:fade-in-0 data-[closed]:zoom-out-95 data-[expanded]:zoom-in-95 data-[closed]:slide-out-to-left-1/2 data-[closed]:slide-out-to-top-[48%] data-[expanded]:slide-in-from-left-1/2 data-[expanded]:slide-in-from-top-[48%] pointer-events-auto fixed top-1/2 left-1/2 z-[9999] grid w-full max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 border border-gray-200 bg-white p-6 shadow-lg duration-200 sm:rounded-lg md:w-full"><span data-focus-trap tabindex=0 class=sr-only></span><span data-focus-trap tabindex=0 class=sr-only>');function an(e){const t="alert-dialog";function n(){return e.isOpen??!1}function r(){return e.isClosable??!0}function s(){return e.closeOnOverlayClick??!0}function i(){return e.closeOnEscape??!0}function o(){r()&&e.closeHandler?.()}Po(i()?o:void 0);function l(){switch(e.buttonsAlignment){case"start":return"justify-start";case"center":return"justify-center";default:return"justify-end"}}return f(L,{get when(){return n()},get children(){return[(()=>{var a=Do();return ot(a,"click",s()?o:void 0,!0),N(()=>G(a,"data-expanded",e.isOpen?"":void 0)),a})(),(()=>{var a=zo(),c=a.firstChild,u=c.nextSibling;return b(a,f(L,{get when(){return e.title},get children(){var d=Lo();return b(d,()=>e.title),d}}),u),b(a,f(L,{get when(){return e.description},get children(){var d=No();return b(d,()=>e.description),d}}),u),b(a,f(L,{get when(){return e.buttons&&e.buttons.length>0},get children(){var d=jo();return b(d,f(Nt,{get each(){return e.buttons},children:h=>h})),N(()=>J(d,`relative flex w-full flex-wrap ${l()} gap-1.5`)),d}}),u),b(a,f(L,{get when(){return r()},get children(){var d=Uo(),h=d.firstChild;return d.$$click=o,b(d,f(hi,{class:"size-4"}),h),d}}),u),N(d=>{var h=e.isOpen?"":void 0,p=e.title?`${t}-title`:void 0,$=e.description?`${t}-description`:void 0;return h!==d.e&&G(a,"data-expanded",d.e=h),p!==d.t&&G(a,"aria-labelledby",d.t=p),$!==d.a&&G(a,"aria-describedby",d.a=$),d},{e:void 0,t:void 0,a:void 0}),a})()]}})}be(["click"]);var Fo=C(`<div class="mx-auto max-w-md bg-white p-6"><div class="mb-6 flex items-center gap-3"><div class="rounded-lg bg-red-100 p-2"></div><h2 class="text-xl font-semibold text-gray-900">Inactive Users Criteria</h2></div><div class=mb-6><div class="mb-4 flex items-center gap-2"><div class="h-2 w-2 rounded-full bg-red-500"></div><h3 class="text-sm font-medium tracking-wide text-gray-900 uppercase">Required Filter</h3></div><div class=space-y-3><div class="flex items-center gap-3 rounded-lg border border-red-100 bg-red-50 p-3"><span class="text-sm text-gray-700">User never submitted a photo</span></div><div class="flex items-center gap-3 rounded-lg border border-red-100 bg-red-50 p-3"><span class="text-sm text-gray-700">User never liked a photo</span></div><div class="flex items-center gap-3 rounded-lg border border-red-100 bg-red-50 p-3"><span class="text-sm text-gray-700">User never followed a user</span></div><div class="flex items-center gap-3 rounded-lg border border-red-100 bg-red-50 p-3"><span class="text-sm text-gray-700">User never commented on a photo</span></div></div></div><div><div class="mb-4 flex items-center gap-2"><div class="h-2 w-2 rounded-full bg-gray-400"></div><h3 class="text-sm font-medium tracking-wide text-gray-600 uppercase">Optional Filter</h3><span class="rounded-full bg-gray-200 px-2 py-1 text-xs text-gray-600">Currently Disabled</span></div><div class=space-y-3><div class="flex items-center gap-3 rounded-lg border border-gray-200 bg-gray-50 p-3 opacity-60"><span class="text-sm text-gray-600">User doesn't have bio (description) in wp_artist_attributes table</span></div><div class="flex items-center gap-3 rounded-lg border border-gray-200 bg-gray-50 p-3 opacity-60"><span class="text-sm text-gray-600">User doesn't have a bio (description) in wp_usermeta table`);function qo(){return(()=>{var e=Fo(),t=e.firstChild,n=t.firstChild,r=t.nextSibling,s=r.firstChild,i=s.nextSibling,o=i.firstChild,l=o.firstChild,a=o.nextSibling,c=a.firstChild,u=a.nextSibling,d=u.firstChild,h=u.nextSibling,p=h.firstChild,$=r.nextSibling,v=$.firstChild,_=v.nextSibling,O=_.firstChild,k=O.firstChild,D=O.nextSibling,P=D.firstChild;return b(n,f(qt,{class:"h-6 w-6 text-red-600"})),b(o,f(_s,{class:"h-4 w-4 flex-shrink-0 text-red-600"}),l),b(a,f(Ds,{class:"h-4 w-4 flex-shrink-0 text-red-600"}),c),b(u,f(ai,{class:"h-4 w-4 flex-shrink-0 text-red-600"}),d),b(h,f(Gs,{class:"h-4 w-4 flex-shrink-0 text-red-600"}),p),b(O,f(Rs,{class:"h-4 w-4 flex-shrink-0 text-gray-500"}),k),b(D,f(Ms,{class:"h-4 w-4 flex-shrink-0 text-gray-500"}),P),e})()}var Wo=C('<header class="mb-4 border-b border-stone-300 px-4 pb-4 md:px-0"><div class="flex w-full items-center justify-between"><h1 class="mb-2 text-2xl font-semibold">Inactive Users</h1><button type=button class="rounded-full bg-stone-100 p-2 hover:bg-stone-200 active:bg-stone-300">'),Bo=C('<div class="mb-4 flex items-center justify-between border-b border-stone-300 px-4 pb-4 md:px-0"><div> user<!> selected</div><div><button type=button class="delete-all-button bg-danger hover:bg-danger-hover active:bg-danger-hover relative inline-flex items-center rounded-sm px-3 py-1 text-sm text-white transition-colors">Delete <!> user'),Ho=C('<div class="vt-tr py-4 text-center">'),Vo=C('<div class="vt-tbody relative w-full">'),Yo=C('<div class="vt-table-wrapper relative w-full overflow-x-auto"><div class="vt-table relative w-[1390px]"><div class="vt-thead bg-pmLightColor w-full rounded-md"><div class="vt-tr flex w-full items-center px-2 py-2 text-left text-sm font-medium"><div class="vt-th inline-flex w-[3.5%] items-center px-1 text-center"><input type=checkbox name=select_all></div><div class="vt-th w-[4.5%] px-1">ID</div><div class="vt-th w-[10%] px-2">Registered date</div><div class="vt-th w-[10%] px-2">Session start</div><div class="vt-th w-[10%] px-2">Session exp.</div><div class="vt-th w-[7%] px-2 text-center">Logged-in</div><div class="vt-th w-[13%] px-2">Display name</div><div class="vt-th w-[17%] px-2">Email</div><div class="vt-th w-[12%] px-2">Instagram</div><div class="vt-th w-[6%] px-2 text-center">Verified</div><div class="vt-th w-[7%] px-2 text-center">Action</div></div></div><div class="vt-scrollable w-full overflow-y-auto">'),Ko=C("<div>"),Go=C('<button type=button class="border-danger text-danger hover:border-danger hover:bg-danger-hover inline-flex items-center justify-center rounded-sm border border-solid bg-white px-3 py-1.5 text-xs transition-colors hover:text-white">'),Jo=C("<span class>Delete permanently"),Xo=C('<button type=button class="border-lhBorderColor text-lhBlack hover:bg-lhLightColor ml-2 inline-flex items-center justify-center rounded-sm border border-solid bg-white px-3 py-1.5 text-xs transition-colors">Cancel'),Zo=C('<button type=button class="border-lhBorderColor text-lhBlack hover:bg-lhLightColor inline-flex items-center justify-center rounded-sm border border-solid bg-white px-3 py-1.5 text-xs transition-colors">OK');function Qo(){const[e,t]=U([]),[n,r]=U(!1),[s,i]=U(!1),[o,l]=U(),[a,c]=U(),[u,d]=U(!1),[h,p]=U(!1);function $(){return s()||n()||!!o()||!!a()||u()||h()}const[v,_]=U([]),[O,k]=U(!0),[D,P]=U(void 0),[F,K]=U(!1);function ae(g){_(A=>A.includes(g)?A.filter(E=>E!==g):[...A,g])}let ce;async function q(){try{Oe(()=>{k(!0),P(void 0)});const g=await Qi();if(!g.success||!g.data){P({message:g.message??"",type:"error"});return}t(g.data.map(A=>Ro(A)))}catch(g){P({message:je(g),type:"error"})}finally{k(!1)}}Tt(async()=>{await q()});const R=H(()=>{let g=e();if(!$())return g;n()&&(g=g.filter(T=>!T.verified)),s()&&(g=g.filter(T=>!T.hasActiveSession));const A=o();A&&(g=g.filter(T=>T.sessionExpiryDate&&new Date(T.sessionExpiryDate)<=new Date(A)));const E=a();return E&&(g=g.filter(T=>T.email.split("@")[0].split(".").length-1>=E)),u()&&(g=g.filter(T=>!T.instagram)),h()&&(g=g.filter(T=>!T.hasAvatar)),g}),I=H(()=>Zi({get count(){return R().length},estimateSize:()=>50,overscan:10})),[W,ee]=U(void 0),[w,m]=U(),[y,S]=U(!1);function x(g){Oe(()=>{m(g),ee("single")})}async function M(){const g=W();if(!g)return;const A=w()?.id;if(g==="single"&&!A||y())return;Oe(()=>{S(!0),P(void 0)});let E;g==="single"?A?E=await eo(A):await new Promise(T=>setTimeout(T,1e3)):E=await to(v()),Oe(()=>{if(P({message:E?.message??"",type:E?.success?"success":"error"}),E&&E.success){if(g==="single")t(T=>T.filter(B=>B.id!==A));else if(g==="bulk"&&E.data){const T=v();if(!E.data.totalFailed)t(B=>B.filter(V=>!T.includes(V.id))),_([]);else{const B=T.filter(V=>!E.data?.idsFailed.includes(V));t(V=>V.filter(te=>!B.includes(te.id))),_(E.data.idsFailed)}}}window.scrollTo(0,0),g==="single"&&m(void 0),S(!1),ee(void 0)})}return[(()=>{var g=Wo(),A=g.firstChild,E=A.firstChild,T=E.nextSibling;return T.$$click=()=>K(!F()),b(T,f(Cs,{size:20})),b(g,f(Ao,{get loading(){return O()},get totalInactiveUsers(){return R().length},get showOnlyUnverified(){return n()},get showOnlyNotLoggedIn(){return s()},get showOnlyWithMaxLastSession(){return!!o()},get showOnlyWithMinDots(){return!!a()},get showOnlyWithoutIg(){return u()},get showOnlyWithoutAvatar(){return h()},get hasActiveFilter(){return $()}}),null),b(g,f(so,{get enableUnverified(){return n()},get enableNotLoggedIn(){return s()},get maxLastSession(){return o()},get minDots(){return a()},get withoutIg(){return u()},get withoutAvatar(){return h()},onUnverifiedFilterChange:r,onNotLoggedInFilterChange:i,onMaxLastSessionFilterChange:l,onMinDotsFilterChange:c,onWithoutIgFilterChange:d,onWithoutAvatarFilterChange:p}),null),g})(),f(L,{get when(){return v().length},get children(){var g=Bo(),A=g.firstChild,E=A.firstChild,T=E.nextSibling;T.nextSibling;var B=A.nextSibling,V=B.firstChild,te=V.firstChild,ie=te.nextSibling;return ie.nextSibling,b(A,()=>v().length,E),b(A,()=>v().length>1?"s":"",T),V.$$click=()=>ee("bulk"),b(V,f(Dn,{size:14,class:"relative top-[-1px] mr-1"}),te),b(V,()=>v().length,ie),b(V,()=>v().length>1?"s":"",null),g}}),f(L,{get when(){return D()},get children(){return f(pi,{get message(){return D()?.message??""},get type(){return D()?.type},class:"mb-4"})}}),(()=>{var g=Yo(),A=g.firstChild,E=A.firstChild,T=E.firstChild,B=T.firstChild,V=B.firstChild,te=E.nextSibling;V.addEventListener("change",se=>{_(se.target.checked?R().map(X=>X.id):[])});var ie=ce;return typeof ie=="function"?wn(ie,te):ce=te,b(te,f(L,{get when(){return O()},get children(){var se=Ho();return b(se,f(Ct,{})),se}}),null),b(te,f(L,{get when(){return!O()},get children(){var se=Vo();return b(se,f(ar,{get each(){return I().getVirtualItems()},children:X=>{const Z=()=>R()[X().index],ue=X().index;return(()=>{var oe=Ko();return J(oe,`vt-tr absolute top-0 left-0 flex w-full items-center px-2 hover:bg-stone-100 ${ue===0?"":"border-pmBaseColor border-t"}`),b(oe,f(bo,{get user(){return Z()},index:ue,get selected(){return v().includes(Z().id)},onCheckboxClick:ae,onDeleteButtonClick:x})),N(fe=>{var at=`${X().size}px`,ct=`translateY(${X().start-I().options.scrollMargin}px)`;return at!==fe.e&&((fe.e=at)!=null?oe.style.setProperty("height",at):oe.style.removeProperty("height")),ct!==fe.t&&((fe.t=ct)!=null?oe.style.setProperty("transform",ct):oe.style.removeProperty("transform")),fe},{e:void 0,t:void 0}),oe})()}})),N(X=>(X=`${I().getTotalSize()}px`)!=null?se.style.setProperty("height",X):se.style.removeProperty("height")),se}}),null),g})(),f(L,{get when(){return W()},get children(){return f(an,{get title(){return`${W()==="bulk"?"Bulk delete users":"Delete user"}`},get description(){return`${W()==="bulk"?`Are you sure you want to permanently delete these ${v().length} users?`:`Are you sure you want to permanently delete ${w()?.displayName??"this user"}?`}`},get buttons(){return[(()=>{var g=Go();return g.$$click=M,b(g,f(L,{get when(){return y()},get fallback(){return Jo()},get children(){return f(Ct,{colorClass:"text-danger",sizeClass:"h-4 w-4"})}})),N(()=>g.disabled=y()),g})(),(()=>{var g=Xo();return g.$$click=()=>ee(void 0),g})()]},isOpen:!0,closeHandler:()=>ee(void 0)})}}),f(L,{get when(){return F()},get children(){return f(an,{title:"Information",get description(){return f(qo,{})},get buttons(){return[(()=>{var g=Zo();return g.$$click=()=>K(!1),g})()]},isOpen:!0,closeHandler:()=>K(!1)})}})]}be(["click"]);var el=C("<div>"),tl=C('<div class=container><div class="relative block w-full lg:flex"><div><img class="mb-14 max-h-11 w-auto"alt="Portrait Mode Logo"><nav class=sidebar-menu><h4 class="text-lg font-semibold">Artists</h4><ul class="mt-4 text-sm font-medium"><li class="-ml-4 block"></li><li class="-ml-4 block"></li></ul></nav><nav class="sidebar-menu mt-8"><h4 class="text-lg font-semibold">Website</h4><ul class="mt-4 text-sm font-medium"><li class="-ml-4 block"></li><li class="-ml-4 block"></li></ul></nav></div><main class="main-area relative w-full pt-12 pb-16 lg:w-9/12 lg:px-5 lg:pb-12 xl:px-10 2xl:w-10/12">');const nl=e=>{const[t,n]=U(!1),r=()=>{n(!t())};return[(()=>{var s=el();return s.$$click=r,N(()=>J(s,`overlay fixed inset-0 z-[9998] bg-black/50 ${t()?"visible opacity-100":"invisible opacity-0"} transition-opacity duration-300 lg:hidden`)),s})(),(()=>{var s=tl(),i=s.firstChild,o=i.firstChild,l=o.firstChild,a=l.nextSibling,c=a.firstChild,u=c.nextSibling,d=u.firstChild,h=d.nextSibling,p=a.nextSibling,$=p.firstChild,v=$.nextSibling,_=v.firstChild,O=_.nextSibling,k=o.nextSibling;return G(l,"src",gs),b(d,f(qe,{get icon(){return f(Ln,{class:"h-5 w-5"})},text:"Pro Members",href:"/dev/pro-members",onClick:r})),b(h,f(qe,{get icon(){return f(qt,{class:"h-5 w-5"})},text:"Inactive Users",href:"/dev/inactive-users",onClick:r})),b(_,f(qe,{get icon(){return f(Pn,{class:"h-5 w-5"})},text:"Home",get href(){return ge.siteUrl},reload:!0,onClick:r})),b(O,f(qe,{get icon(){return f(Ws,{class:"h-5 w-5"})},text:"Admin Area",get href(){return`${ge.siteUrl}/wp-admin`},reload:!0,onClick:r})),b(k,()=>e.children),N(()=>J(o,`sidebar fixed ${t()?"visible left-0 opacity-100":"invisible -left-full opacity-0"} bg-pmBaseColorAlt top-0 z-[9999] min-h-screen w-9/12 border-r border-stone-300 px-5 py-12 transition-all duration-300 sm:w-7/12 lg:visible lg:relative lg:left-0 lg:w-3/12 lg:opacity-100 xl:px-10 2xl:w-2/12`)),s})(),f(yi,{onHamburgerMenuClick:s=>{s.preventDefault(),r()}})]},rl=()=>f(os,{root:nl,explicitLinks:!0,get children(){return[f(ze,{path:"/",component:pt}),f(ze,{path:"/dev",component:pt}),f(ze,{path:"/dev/pro-members",component:pt}),f(ze,{path:"/dev/inactive-users",component:Qo})]}});be(["click"]);const cn=document.getElementById("root");cn&&vr(()=>f(rl,{}),cn);
