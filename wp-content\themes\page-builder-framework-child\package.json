{"name": "portraitmode", "version": "1.53.0", "description": "Development tooling for PortraitMode website", "author": "<PERSON>", "license": "GPL-3.0", "dependencies": {"@clack/prompts": "^0.10.1", "@parcel/core": "^2.15.4", "@parcel/source-map": "^2.1.1", "@preact/signals-core": "^1.11.0", "@selectize/selectize": "^0.15.2", "@solidjs/router": "^0.15.3", "axios": "^1.10.0", "browserslist": "^4.25.1", "chalk": "^5.4.1", "chalk-template": "^1.1.0", "chart.js": "^4.5.0", "chokidar": "^4.0.3", "chokidar-cli": "^3.0.0", "cli-spinners": "^3.2.0", "esbuild": "^0.25.5", "flatpickr": "^4.6.13", "flexmasonry": "^0.2.3", "flickity": "^3.0.0", "flickity-fade": "^2.0.0", "fuse.js": "^7.1.0", "glob": "^11.0.3", "jquery": "^3.7.1", "jsx-dom": "^8.1.6", "ky": "^1.8.1", "lightningcss": "^1.30.1", "lightningcss-cli": "^1.30.1", "lucide-solid": "^0.477.0", "mitt": "^3.0.1", "ora": "^8.2.0", "prettier": "^3.6.2", "sass": "^1.89.2", "shelljs": "^0.8.5", "simplebar": "^6.3.2", "solid-collapse": "^1.1.0", "solid-icons": "^1.1.0", "solid-js": "^1.9.7", "source-map-js": "^1.2.1", "ssh2": "^1.16.0", "ssh2-sftp-client": "^11.0.0", "timeago.js": "^4.0.2", "tributejs": "^5.1.3"}, "devDependencies": {"@babel/core": "^7.28.0", "@parcel/config-default": "^2.15.4", "@parcel/transformer-sass": "^2.15.4", "@parcel/types": "^2.15.4", "@types/flickity": "^2.2.11", "@types/google.accounts": "^0.0.15", "@types/jquery": "^3.5.32", "@types/selectize": "^0.12.39", "@types/shelljs": "^0.8.17", "@types/ssh2-sftp-client": "^9.0.4", "babel-preset-solid": "^1.9.6", "browser-sync": "^3.0.4", "parcel": "^2.15.4", "parcel-resolver-ignore": "^2.2.0", "solid-devtools": "^0.33.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-solid": "^2.11.7"}, "alias": {"jquery": {"global": "j<PERSON><PERSON><PERSON>"}, "wp": {"global": "wp"}, "axios": {"global": "axios"}, "tributejs": {"global": "Tribute"}, "flickity": {"global": "Flickity"}}, "parcelIgnore": ["fonts/.+"], "bin": {"pm": "cli.mjs", "serve-staging": "serve-staging.mjs", "serve-live": "serve-live.mjs"}, "scripts": {"pm": "node cli.mjs", "serve-staging": "node serve-staging.mjs", "serve-live": "node serve-live.mjs", "build-file": "vite build", "watch": "parcel watch ./src/*.ts ./src/*.tsx --dist-dir ./dist", "build": "parcel build ./src/*.ts ./src/*.tsx --dist-dir ./dist", "build-style": "parcel build ./assets/scss/style.scss --dist-dir ./css", "watch-dev": "parcel watch ./dev/src/index.html --dist-dir ./dev/watch", "build-dev": "parcel build ./dev/src/index.html --dist-dir ./dev/dist", "watch-global-utils": "parcel watch ./src/global-utils.ts --dist-dir ./dist", "build-global-utils": "parcel build ./src/global-utils.ts --dist-dir ./dist", "watch-site": "parcel watch ./src/site.ts --dist-dir ./dist", "build-site": "parcel build ./src/site.ts --dist-dir ./dist", "watch-potd": "parcel watch ./src/potd.ts --dist-dir ./dist", "build-potd": "parcel build ./src/potd.ts --dist-dir ./dist", "watch-album-edit": "parcel watch ./src/album-edit.ts --dist-dir ./dist", "build-album-edit": "parcel build ./src/album-edit.ts --dist-dir ./dist", "watch-album-view": "parcel watch ./src/album-view.ts --dist-dir ./dist", "build-album-view": "parcel build ./src/album-view.ts --dist-dir ./dist", "watch-load-more": "parcel watch ./src/load-more.ts --dist-dir ./watch", "build-load-more": "parcel build ./src/load-more.ts --dist-dir ./dist", "watch-search": "parcel watch ./src/search.ts --dist-dir ./watch", "build-search": "parcel build ./src/search.ts --dist-dir ./dist", "watch-review-photo": "parcel watch ./src/review-photo.ts --dist-dir ./dist", "build-review-photo": "parcel build ./src/review-photo.ts --dist-dir ./dist", "watch-featured-photo-review": "parcel watch ./src/featured-photo-review.ts --dist-dir ./dist", "build-featured-photo-review": "parcel build ./src/featured-photo-review.ts --dist-dir ./dist", "watch-frontend-photo-review": "parcel watch ./src/frontend-photo-review.ts --dist-dir ./dist", "build-frontend-photo-review": "parcel build ./src/frontend-photo-review.ts --dist-dir ./dist", "watch-media-library-photo-review": "parcel watch ./src/media-library-photo-review.ts --dist-dir ./dist", "build-media-library-photo-review": "parcel build ./src/media-library-photo-review.ts --dist-dir ./dist", "watch-review-moderation": "parcel watch ./src/review-moderaticon.tsx --dist-dir ./dist", "build-review-moderation": "parcel build ./src/review-moderation.tsx --dist-dir ./dist", "watch-profile": "parcel watch ./src/profile.ts --dist-dir ./dist", "build-profile": "parcel build ./src/profile.ts --dist-dir ./dist", "watch-edit-profile": "parcel watch ./src/edit-profile.ts --dist-dir ./dist", "build-edit-profile": "parcel build ./src/edit-profile.ts --dist-dir ./dist", "watch-photo-upload": "parcel watch ./src/photo-upload.ts --dist-dir ./dist", "build-photo-upload": "parcel build ./src/photo-upload.ts --dist-dir ./dist", "watch-youtube-upload": "parcel watch ./src/youtube-upload.ts --dist-dir ./dist", "build-youtube-upload": "parcel build ./src/youtube-upload.ts --dist-dir ./dist", "watch-categories": "parcel watch ./src/categories.ts --dist-dir ./dist", "build-categories": "parcel build ./src/categories.ts --dist-dir ./dist", "watch-review-categories": "parcel watch ./src/review-categories.ts --dist-dir ./dist", "build-review-categories": "parcel build ./src/review-categories.ts --dist-dir ./dist", "watch-review-ads": "parcel watch ./src/review-spc.tsx --dist-dir ./dist", "build-review-ads": "parcel build ./src/review-spc.tsx --dist-dir ./dist", "watch-membership": "parcel watch ./src/membership.ts --dist-dir ./dist", "build-membership": "parcel build ./src/membership.ts --dist-dir ./dist", "watch-contact-form": "parcel watch ./src/contact-form.ts --dist-dir ./dist", "build-contact-form": "parcel build ./src/contact-form.ts --dist-dir ./dist", "watch-subscribe-form": "parcel watch ./src/subscribe-form.ts --dist-dir ./dist", "build-subscribe-form": "parcel build ./src/subscribe-form.ts --dist-dir ./dist", "watch-registration-popup": "parcel watch ./src/registration-popup.ts --dist-dir ./dist", "build-registration-popup": "parcel build ./src/registration-popup.ts --dist-dir ./dist", "watch-youtube-verification": "parcel watch ./src/youtube-verification.ts --dist-dir ./dist", "build-youtube-verification": "parcel build ./src/youtube-verification.ts --dist-dir ./dist", "watch-manual-youtube-channel-verification": "parcel watch ./src/manual-youtube-channel-verification.ts --dist-dir ./dist", "build-manual-youtube-channel-verification": "parcel build ./src/manual-youtube-channel-verification.ts --dist-dir ./dist", "watch-manual-youtube-channel-verification-review": "parcel watch ./src/manual-youtube-channel-verification-review.ts --dist-dir ./dist", "build-manual-youtube-channel-verification-review": "parcel build ./src/manual-youtube-channel-verification-review.ts --dist-dir ./dist", "watch-delete-account": "parcel watch ./src/delete-account.ts --dist-dir ./dist", "build-delete-account": "parcel build ./src/delete-account.ts --dist-dir ./dist", "watch-sponsor": "parcel watch ./src/sponsor.ts --dist-dir ./dist", "build-sponsor": "parcel build ./src/sponsor.ts --dist-dir ./dist", "watch-statistics": "parcel watch ./src/statistics.ts --dist-dir ./dist", "build-statistics": "parcel build ./src/statistics.ts --dist-dir ./dist"}, "browserslist": ["> 0.2% and not dead"], "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "@swc/core", "cpu-features", "esbuild", "lightningcss-cli", "lmdb", "msgpackr-extract", "ssh2"]}}