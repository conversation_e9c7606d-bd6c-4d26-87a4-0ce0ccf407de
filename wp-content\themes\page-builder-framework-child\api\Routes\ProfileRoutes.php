<?php

declare( strict_types = 1 );

namespace Pm\Api\Routes;

defined( 'ABSPATH' ) || die( "Can't access directly" );

use Pm\Api\Controllers\MyProfileController;
use WP_REST_Server;

final class ProfileRoutes
{
	public function __construct(
		private readonly string $rootApiUrl,
	) {
		$profileController = new MyProfileController();

		register_rest_route(
			$this->rootApiUrl,
			'/my-profile/liked-photos/(?P<limit>-?\d+)/(?P<last_id>-?\d+)',
			[
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => [ $profileController, 'likedPhotos' ],
				'permission_callback' => '__return_true',
				'args'                => [
					'limit'   => [
						'type' => 'integer',
					],
					'last_id' => [
						'type' => 'integer',
					],
				],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/my-profile/archived-photos/(?P<limit>-?\d+)/(?P<last_id>-?\d+)',
			[
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => [ $profileController, 'archivedPhotos' ],
				'permission_callback' => '__return_true',
				'args'                => [
					'limit'   => [
						'type' => 'integer',
					],
					'last_id' => [
						'type' => 'integer',
					],
				],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/followings/(?P<limit>-?\d+)/(?P<last_id>-?\d+)/(?P<last_total_photos>-?\d+)',
			[
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => [ $profileController, 'followings' ],
				'permission_callback' => '__return_true',
				'args'                => [
					'limit'             => [
						'type' => 'integer',
					],
					'last_id'           => [
						'type' => 'integer',
					],
					'last_total_photos' => [
						'type' => 'integer',
					],
				],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/followers/(?P<limit>-?\d+)/(?P<last_id>-?\d+)/(?P<last_total_photos>-?\d+)',
			[
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => [ $profileController, 'followers' ],
				'permission_callback' => '__return_true',
				'args'                => [
					'limit'             => [
						'type' => 'integer',
					],
					'last_id'           => [
						'type' => 'integer',
					],
					'last_total_photos' => [
						'type' => 'integer',
					],
				],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/my-profile/edit',
			[
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => [ $profileController, 'edit' ],
				'permission_callback' => '__return_true',
				'args'                => [],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/my-profile/avatar',
			[
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => [ $profileController, 'uploadAvatar' ],
				'permission_callback' => '__return_true',
				'args'                => [],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/my-profile/avatar',
			[
				'methods'             => WP_REST_Server::DELETABLE,
				'callback'            => [ $profileController, 'deleteAvatar' ],
				'permission_callback' => '__return_true',
				'args'                => [],
			]
		);

		register_rest_route(
			$this->rootApiUrl,
			'/my-profile',
			[
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => [ $profileController, 'get' ],
				'permission_callback' => '__return_true',
			]
		);
	}
}
