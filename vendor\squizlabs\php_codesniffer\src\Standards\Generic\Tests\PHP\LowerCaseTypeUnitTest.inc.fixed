<?php
$foo = (int) $bar;
$foo = (integer) $bar;
$foo = (bool) $bar;
$foo = (boolean) $bar;
$foo = (float) $bar;
$foo = (double) $bar;
$foo = (real) $bar;
$foo = (string) $bar;
$foo = (array) $bar;
$foo = (object) $bar;
$foo = (unset) $bar;

$foo = (int) $bar;
$foo = (integer) $bar;
$foo = (bool) $bar;
$foo = (string) $bar;
$foo = ( array ) $bar;

function foo(int $a, string $b, bool $c, array $d, Foo\Bar $e) : int {}
function foo(int $a, string $b, bool $c, array $d, Foo\Bar $e) : Foo\Bar {}
function foo(int $a, Bar $b, bool $c, array $d, Foo\Bar $e) : Bar {}
function foo(callable $a, callable $b, self $c, iterable $d, iterable $e) : float {}

$foo = function (int $a, bool $b) {};
$foo = function (int $a, callable $b) :int{};
$foo = function (bool $a, float $b) use ($foo) : int {};
$foo = function (Foo $a, Foo\Bar $b) use ($foo) : \Foo\Bar {};
$foo = function (bool $a, callable $b) use ($foo) : Bar {};

class Testing {
    public function TestThis(self $a, object $b, parent $c) : void {}
}

function foo(
    ?float $a,
    ? string $b,
    ?iterable $c,
    ?	object $d,
    ?Foo\Bar $e
) : ?Foo\Bar {}

$foo = function (?int $a, ?    callable $b)
    :?int{};

$var = (binary) $string;
$var = (binary)$string;

function unionParamTypesA (bool|array| /* nullability operator not allowed in union */ null $var) {}

function unionParamTypesB (\Package\ClassName | int | \Package\Other_Class | false $var) {}

function unionReturnTypesA ($var): bool|array| /* nullability operator not allowed in union */ null {}

function unionReturnTypesB ($var): \Package\ClassName | int | \Package\Other_Class | false {}

class TypedProperties
{
    protected ClassName $class;
    public int $int;
    private ?bool $bool;
    public self $self;
    protected parent $parent;
    private array $array;
    public float $float;
    protected ?string $string;
    private iterable $iterable;
    public object $object;
    protected mixed $mixed;

    public iterable|false|null $unionTypeA;
    protected self|parent /* comment */ |\Fully\Qualified\ClassName|UnQualifiedClass $unionTypeB;
    private ClassName|/*comment*/float|string|false $unionTypeC;
    public string | array | false $unionTypeD;
}

class ConstructorPropertyPromotionWithTypes {
    public function __construct(protected float|int $x, public ?string &$y = 'test', private mixed $z) {}
}

class ConstructorPropertyPromotionAndNormalParams {
    public function __construct(public int $promotedProp, ?int $normalArg) {}
}

function (): never {
    exit;
};

function intersectionParamTypes (\Package\ClassName&\Package\Other_Class $var) {}

function intersectionReturnTypes ($var): \Package\ClassName&\Package\Other_Class {}

$arrow = fn (int $a, string $b, bool $c, array $d, Foo\Bar $e) : int => $a * $b;
$arrow = fn (int $a, string $b, bool $c, array $d, Foo\Bar $e) : float => $a * $b;

$cl = function (false $a, true $b, null $c): ?true {};

class TypedClassConstants
{
    const UNTYPED = null;
    const FLOAT = 'Reserved keyword as name is valid and should not be changed';
    const OBJECT = 'Reserved keyword as name is valid and should not be changed';

    const ClassName FIRST = null;
    public const int SECOND = 0;
    private const ?bool THIRD = false;
    public const self FOURTH = null;
}
interface TypedInterfaceConstants
{
    protected const parent FIRST = null;
    private const array SECOND = [];
    public const float THIRD = 2.5;
    final const ?string FOURTH = 'fourth';
}
trait TypedTraitConstants {
    const iterable FIRST = null;
    const object SECOND = null;
    const mixed THIRD = 'third';
}
enum TypedEnumConstants {
    public const iterable|false|null FIRST = null;
    protected const self|parent /* comment */ |\Fully\Qualified\ClassName|UnQualifiedClass SECOND = null;
    private const ClassName|/*comment*/float|string|false THIRD = 'third';
    public const string | array | false FOURTH = 'fourth';
}

class DNFTypes {
    const (parent&Something)|float CONST_NAME = 1.5;

    public readonly true|(\A&B) $prop;

    function DNFParamTypes (
        null|(\Package\ClassName&\Package\Other_Class)|int $DNFinMiddle,
        (\Package\ClassName&\Package\Other_Class)|array $parensAtStart,
        false|(\Package\ClassName&\Package\Other_Class) $parentAtEnd,
    ) {}

    function DNFReturnTypes ($var): object|(self&\Package\Other_Class)|string|false {}
}

// Intentional error, should be ignored by the sniff.
interface PropertiesNotAllowed {
    public $notAllowed;
}
