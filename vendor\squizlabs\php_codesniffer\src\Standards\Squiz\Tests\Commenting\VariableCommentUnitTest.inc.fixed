<?php
class VariableCommentUnitTest
{

    /**
     * Short description of the member variable.
     *
     * Long description of member variable. Can span over multiple
     * lines and can have multiple paragraphs.
     *
     * @var array
     * @see otherFunctions()
     * @see anotherFunctions()
     */
     public $variableName = array();


     // Not "/**" style comment.
     //
     // @var   string
     private  $_incorrectCommentStyle = '';


     protected $noComment = '';


    /**
     *
     * Extra newline before short comment.
     *
     * @var string
     */
     public $extraNewlineBeforeShort = '';


    /**
     * Extra newline between short description.
     *
     *
     *
     * Long description.
     *
     * @var string
     */
     private $_extraNewlineBetween = '';


    /**
     * Extra newline before tags.
     *
     * Long description,
     * with two var tags.
     *
     *
     * @var string
     * @var array
     */
     protected $extraNewlineBeforeTags = '';


    /**
     * No newline before tags, var tag missing.
     * @see otherFunctions()
     */
     protected $noNewlineBeforeTags = '';


    /**
     * Short comment that spans multiple
     * lines and does not end with a full stop
     * also var type is missing
     *
     * @var
     */
     public $singleLineFullStopShortComment = '';


    /**
     * Incorrect tag indentation.
     *
     * @var     string
     * @see        otherFunctions()
     * @see anotherFunctions()
     * @see
     */
     public $singleLineFullStopShortComment = '';


    /**
     * Unknown summary tag
     *
     * @var     string
     * @summary unknown tag
     */
     public $missingSinceTag = '';


    /**
     * T_VARIABLE check, without scope.
     *
     * @var string
     */
     $variableCheck = '';

    /**
     * T_VARIABLE check, var in string and in function.
     *
     * @param integer $var1 First variable.
     * @param integer $var2 Second variable.
     *
     * @return integer
     */
    protected function checkVariable($var1, $var2)
    {
        $var4 = 'A normal variable';
        $var5 = PHP_CodeSniffer_Tokens::$operators;
        echo "Printing $var1 $var2 and unknown $var3 in a double quoted  string\n";
        foreach (self::$_array as $index => $content) {
            echo $content;
        }

        return $var1;

    }//end checkVariable()


    /**
     *
     *
     */
     $emptyVarDoc = '';

     /**
     * Var type checking (int v.s. integer).
     *
     * @var integer
     */
     private $_varSimpleTypeCheck;


     /**
     * Var type checking (array(int => string) v.s. array(int => string)).
     *
     * @var array(integer => string)
     */
     private $_varArrayTypeCheck;


    /**
     * Boolean @var tag Capitalized
     *
     * @var boolean
     */
     public $CapBoolTag = true;


    /**
     * Boolean @var tag Capitalized
     *
     * @var boolean
     */
     public $CapBoolTag2 = true;


    /**
     * Double @var tag Capitalized
     *
     * @var float
     */
     public $CapDoubleTag = 1;


    /**
     * Double @var tag Capitalized
     *
     * @var float
     */
     public $CapDoubleTag2 = 1;


    /**
     * Real @var tag Capitalized
     *
     * @var float
     */
     public $CapRealTag = 1;


    /**
     * Real @var tag Capitalized
     *
     * @var float
     */
     public $CapRealTag2 = 1;


    /**
     * Float @var tag Capitalized
     *
     * @var float
     */
     public $CapFloatTag = 1;


    /**
     * Float @var tag Capitalized
     *
     * @var float
     */
     public $CapFloatTag2 = 1;


    /**
     * Int @var tag Capitalized
     *
     * @var integer
     */
     public $CapIntTag = 1;


    /**
     * Int @var tag Capitalized
     *
     * @var integer
     */
     public $CapIntTag2 = 1;


    /**
     * Integer @var tag Capitalized
     *
     * @var integer
     */
     public $CapIntTag3 = 1;


    /**
     * Integer @var tag Capitalized
     *
     * @var integer
     */
     public $CapIntTag4 = 1;


    /**
     * Array @var tag Capitalized
     *
     * @var array
     */
     public $CapVarTag = [];


    /**
     * Array @var tag All Caps
     *
     * @var array
     */
     public $CapVarTag2 = [];


    /**
     * Array @var tag Capitalized
     *
     * @var array
     */
     public $CapVarTag3 = [];


    /**
     * Array @var tag All Caps
     *
     * @var array
     */
     public $CapVarTag4 = [];


     /**
     * Var type checking (STRING v.s. string).
     *
     * @var string
     */
     private $_varCaseTypeCheck;


    /**
     * @var integer
     */
     private $_varWithNoShortComment;

     protected $noComment2 = '';


     /**
     * @var integer Var type checking (int v.s. integer) with single-line comment.
     */
     private $_varSimpleTypeCheckSingleLine;


}//end class


/**
 * VariableCommentUnitTest2.
 *
 * Long description goes here.
 *
 */
class VariableCommentUnitTest2
{

   public $hello;

    /** Comment starts here.
     *
     * @var string
     *
     */
     private $_varCaseTypeCheck;

     /**
      * 这是一条测试评论.
      *
      * @var string
      */
     public $foo;

}//end class


/*
 * Class comment
 */
class Foo
{

    protected $bar;

    /**
     * Short description of the member variable.
     *
     * @var array
     */
     public static array $variableName = array();

}

class Foo
{

    /**
     * Short description of the member variable.
     *
     * @var array
     */
     public array $variableName = array();


     // Not "/**" style comment.
     //
     // @var   string
     private ?Folder\ClassName $_incorrectCommentStyle = null;


     var int $noComment = 1;
 }

class HasAttributes
{
    /**
     * Short description of the member variable.
     *
     * @var array
     */
    #[ORM\Id]#[ORM\Column("integer")]
    private $id;

    /**
     * Short description of the member variable.
     *
     * @var array
     */
    #[ORM\GeneratedValue]
    #[ORM\Column(ORM\Column::T_INTEGER)]
    protected $height;
}

class ReadOnlyProps
{
    /**
     * Short description of the member variable.
     *
     * @var array
     */
     public readonly array $variableName = array();

    /**
     * Short description of the member variable.
     *
     * @var
     */
     readonly protected ?int $variableName = 10;

     private readonly string $variable;
}

class UnionTypes
{
    /**
     * @var array|boolean
     */
    private array|bool $variableName = array();
}

class IntersectionTypes
{
    /**
     * @var \Iterator|\Countable
     */
    private \Iterator&\Countable $variableName;
}

class StandaloneNullTrueFalseTypes
{
    /**
     * @var null
     */
    public null $variableName = null;

    /**
     * @var true
     */
    protected true $variableName = true;

    /**
     * @var false
     */
    private false $variableName = false;
}

class MoreMissingButSupportedTypes
{
    /**
     * @var parent
     */
    public parent $variableName;

    /**
     * @var self
     */
    protected self $variableName;

    /**
     * @var SomeClass
     */
    private namespace\SomeClass $variableName;
}
