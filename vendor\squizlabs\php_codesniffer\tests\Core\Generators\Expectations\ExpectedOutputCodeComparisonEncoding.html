<html>
 <head>
  <title>GeneratorTest Coding Standards</title>
  <style>
        body {
            background-color: #FFFFFF;
            font-size: 14px;
            font-family: Arial, Helvetica, sans-serif;
            color: #000000;
        }

        h1 {
            color: #666666;
            font-size: 20px;
            font-weight: bold;
            margin-top: 0px;
            background-color: #E6E7E8;
            padding: 20px;
            border: 1px solid #BBBBBB;
        }

        h2 {
            color: #00A5E3;
            font-size: 16px;
            font-weight: normal;
            margin-top: 50px;
        }

        .code-comparison {
            width: 100%;
        }

        .code-comparison td {
            border: 1px solid #CCCCCC;
        }

        .code-comparison-title, .code-comparison-code {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px;
            color: #000000;
            vertical-align: top;
            padding: 4px;
            width: 50%;
            background-color: #F1F1F1;
            line-height: 15px;
        }

        .code-comparison-title {
            text-align: left;
            font-weight: 600;
        }

        .code-comparison-code {
            font-family: Courier;
            background-color: #F9F9F9;
        }

        .code-comparison-highlight {
            background-color: #DDF1F7;
            border: 1px solid #00A5E3;
            line-height: 15px;
        }

        .tag-line {
            text-align: center;
            width: 100%;
            margin-top: 30px;
            font-size: 12px;
        }

        .tag-line a {
            color: #000000;
        }
    </style>
 </head>
 <body>
  <h1>GeneratorTest Coding Standards</h1>
  <a name="Code-Comparison,-char-encoding" />
  <h2>Code Comparison, char encoding</h2>
  <p class="text">This is a standard block.</p>
  <table class="code-comparison">
   <tr>
    <th class="code-comparison-title">Valid: Vestibulum et orci condimentum.</th>
    <th class="code-comparison-title">Invalid: Donec in nisl ut tortor convallis interdum.</th>
   </tr>
   <tr>
    <td class="code-comparison-code">&lt;?php</br></br>//&nbsp;The&nbsp;above&nbsp;PHP&nbsp;tag&nbsp;is&nbsp;specifically&nbsp;testing</br>//&nbsp;handling&nbsp;of&nbsp;that&nbsp;in&nbsp;generated&nbsp;HTML&nbsp;doc.</br></br>//&nbsp;Now&nbsp;let's&nbsp;also&nbsp;check&nbsp;the&nbsp;handling&nbsp;of</br>//&nbsp;comparison&nbsp;operators&nbsp;in&nbsp;code&nbsp;samples...</br>$a&nbsp;=&nbsp;$b&nbsp;<&nbsp;$c;</br>$d&nbsp;=&nbsp;$e&nbsp;>&nbsp;$f;</br>$g&nbsp;=&nbsp;$h&nbsp;<=&nbsp;$i;</br>$j&nbsp;=&nbsp;$k&nbsp;>=&nbsp;$l;</br>$m&nbsp;=&nbsp;$n&nbsp;<=>&nbsp;$o;</td>
    <td class="code-comparison-code"><span class="code-comparison-highlight">&lt;?php</span></br></br>//&nbsp;The&nbsp;above&nbsp;PHP&nbsp;tag&nbsp;is&nbsp;specifically&nbsp;testing</br>//&nbsp;handling&nbsp;of&nbsp;that&nbsp;in&nbsp;generated&nbsp;HTML&nbsp;doc.</br></br>//&nbsp;Now&nbsp;let's&nbsp;also&nbsp;check&nbsp;the&nbsp;handling&nbsp;of</br>//&nbsp;comparison&nbsp;operators&nbsp;in&nbsp;code&nbsp;samples</br>//&nbsp;in&nbsp;combination&nbsp;with&nbsp;"em"&nbsp;tags.</br>$a&nbsp;=&nbsp;$b&nbsp;<span class="code-comparison-highlight"><</span>&nbsp;$c;</br>$d&nbsp;=&nbsp;$e&nbsp;>&nbsp;$f;</br>$g&nbsp;=&nbsp;<span class="code-comparison-highlight">$h&nbsp;<=&nbsp;$i</span>;</br>$j&nbsp;=&nbsp;$k&nbsp;>=&nbsp;$l;</br>$m&nbsp;=&nbsp;$n&nbsp;<span class="code-comparison-highlight"><=></span>&nbsp;$o;</td>
   </tr>
  </table>
  <div class="tag-line">Documentation generated on #REDACTED# by <a href="https://github.com/PHPCSStandards/PHP_CodeSniffer">PHP_CodeSniffer #VERSION#</a></div>
 </body>
</html>
