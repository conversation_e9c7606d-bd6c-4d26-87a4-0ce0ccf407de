<?php



class ClassDeclaration
{

}

abstract class CorrectClassDeclarationWithExtends extends correctClassDeclaration
{

}

final class CorrectClassDeclarationWithImplements implements correctClassDeclaration
{

}

// Incorrect placement of opening/closing braces, including indent.
class IncorrectBracePlacement
{
}

class IncorrectBracePlacementWithExtends extends correctClassDeclaration
{
}

class IncorrectBracePlacementWithImplements implements correctClassDeclaration
{
}

class IncorrectIndentedClass
{

}//end class

// Incorrect code placement for opening/closing brace.
class IncorrectCodeAfterOpeningBrace
{
 echo phpinfo();

}//end class

class IncorrectCodeAfterClosingBrace
{

} 

echo phpinfo();


class IncorrectCodeBeforeClosingBrace
{

echo phpinfo(); 
}

class IncorrectIndentedClass
{

}

class ClassOne implements ClassTwo, ClassThree
{
}//end class

class ClassOne implements ClassFour, ClassFive, ClassSix
{
}//end class

class CorrectClassDeclarationWithExtends extends correctClassDeclaration
{

}

class CorrectClassDeclarationWithExtends extends correctClassDeclaration
{

}//end class

class CorrectClassDeclaration
{

}//end class

class CorrectClassDeclaration extends CorrectClassDeclaration2 implements ICorrectClassDeclaration
{

} // phpcs:enable Standard.Category.Sniff

class File implements \Zend_Auth_Storage_Interface, \Zend_Auth_Storage, \Zend_Foo
{
}

interface MyInterface
{

}

?>

<?php

class MyClass2
{
    var $x;
}


/**
 * No error.
 */
function example() {}

class CorrectClassDeclaration
{

}//end class

// Class comment here, but wrong comment type.
class testing
{
} /* end class */ 

echo 'hi';

class IncorrectCodeBeforeClosingBrace
{

echo phpinfo();
}

readonly class Test
{
}

readonly class Test
{
}
