hoistPattern:
  - '*'
hoistedDependencies: {}
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.5.2
pendingBuilds: []
prunedAt: Sun, 09 Mar 2025 06:58:00 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\www\portraitmode\node_modules\.pnpm
virtualStoreDirMaxLength: 60
