<?php

declare( strict_types = 1 );

namespace Pm\Module\Artist\Utils;

use Pm\Module\Album\Dto\AlbumData;
use Pm\Module\Album\Utils\Album;
use Pm\Module\Artist\Dto\FollowResult;
use Pm\Module\Artist\Table\ArtistAttributeTable;
use Pm\Module\Email\Email;
use Pm\Module\Helper\UserHelper;
use Pm\Module\Token\Services\ArtistTokenService;
use Pm\PmConfig;
use WP_Error;
use WP_User;

final class ArtistUtil
{
	public string $artist_blocks_table = '';

	public function __construct(
		public ?int $id = null,
		public ?int $followerId = null,
	) {
		global $wpdb;

		$this->artist_blocks_table = $wpdb->prefix . 'pm_artist_blocks';
	}

	public static function withId( int $id ): self
	{
		return new self( $id );
	}

	public function exists(): bool
	{
		global $wpdb;

		$user_count = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT COUNT(*) FROM $wpdb->users WHERE ID = %d",
				$this->id
			)
		);

		return $user_count > 0;
	}

	public function getEmail(): string
	{
		global $wpdb;

		$email = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT user_email FROM $wpdb->users WHERE ID = %d",
				$this->id
			)
		);

		return $email === null ? '' : $email;
	}

	public function getNicename(): string
	{
		global $wpdb;

		$email = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT user_nicename FROM $wpdb->users WHERE ID = %d",
				$this->id
			)
		);

		return $email === null ? '' : $email;
	}

	public function getFirstName(): string
	{
		$first_name = ArtistAttributeTable::withId( $this->id )->value( 'first_name' );

		return ! is_string( $first_name ) ? '' : $first_name;
	}

	public function remainingFeedbackTokens(): int
	{
		if ( $this->id === null ) {
			trigger_error( 'The "id" must be set in constructor when calling remainingFeedbackTokens method.', E_USER_WARNING );

			return 0;
		}

		$token = ( new ArtistTokenService() )->find( $this->id );

		if ( ! $token ) {
			return 0;
		}

		return $token->amount;
	}

	public function canRequestFeedback(): bool
	{
		if ( ! $this->id ) {
			return false;
		}

		$token = ( new ArtistTokenService() )->find( $this->id );

		if ( ! $token ) {
			return false;
		}

		$amount_token_required = PmConfig::tokenAmountPerFeedback();

		return $token->amount >= $amount_token_required;
	}

	public function suggestPhotoTobeFeatured( int $photo_id ): bool|WP_Error
	{
		if ( ! pm_photo_exists( $photo_id ) ) {
			return new WP_Error( 'photo_not_found', 'Photo does not exist' );
		}

		if ( ! UserHelper::withId( $this->id )->isCurator() ) {
			return new WP_Error( 'not_curator', 'Not allowed to perform this action' );
		}

		update_post_meta( $photo_id, 'modfeature', 1 );

		$curators = get_post_meta( $photo_id, 'modfeature_by', true );
		$curators = $curators ?: [];

		$curators[] = $this->id;

		update_post_meta( $photo_id, 'modfeature_by', $curators );

		$suggested_photo_ids = get_user_meta( $this->id, 'modfeature_photos', true );
		$suggested_photo_ids = $suggested_photo_ids ?: [];

		$suggested_photo_ids[] = $photo_id;

		update_user_meta( $this->id, 'modfeature_photos', $suggested_photo_ids );

		// This is disabled because David received too many emails.
		// If you want to enable it, don't forget to import the class.
		// Email::photoSuggestedToFeature(
		// 	wp_get_current_user(),
		// 	get_post( $photo_id ),
		// )->toAdmin();

		return true;
	}

	public function reportPhoto( int $photo_id, string $reason, bool $as_curator = false ): bool|WP_Error
	{
		if ( $as_curator ) {
			if ( ! UserHelper::withId( $this->id )->isCurator() ) {
				return new WP_Error( 'not_curator', 'Not allowed to perform this action' );
			}
		}

		$photo = get_post( $photo_id );

		if ( ! $photo ) {
			return new WP_Error( 'photo_not_found', 'Photo does not exist' );
		}

		if ( $as_curator ) {
			update_post_meta( $photo_id, 'modlook', 1 );

			$curators = get_post_meta( $photo_id, 'modlook_by', true );
			$curators = $curators ?: [];

			$curators[ $this->id ] = $reason;

			update_post_meta( $photo_id, 'modlook_by', $curators );

			$reported_photo_ids = get_user_meta( $this->id, 'modlook_photos', true );
			$reported_photo_ids = $reported_photo_ids ?: [];

			$reported_photo_ids[ $photo_id ] = $reason;

			update_user_meta( $this->id, 'modlook_photos', $reported_photo_ids );
		} else {
			update_post_meta( $photo_id, 'reported', 1 );

			$reporters = get_post_meta( $photo_id, 'reported_by', true );
			$reporters = $reporters ?: [];

			$reporters[ $this->id ] = $reason;

			update_post_meta( $photo_id, 'reported_by', $reporters );
		}

		Email::photoReported(
			reporter: get_userdata( $this->id ),
			reason: $reason,
			photo: $photo,
			as_curator: $as_curator,
		)->toAdmin();

		return true;
	}

	public function alreadyBlocked( int $artist_id ): bool
	{
		global $wpdb;

		$prepared_command = $wpdb->prepare(
			"SELECT COUNT(*) FROM $this->artist_blocks_table WHERE blocker_id = %d AND blocked_id = %d",
			$this->id,
			$artist_id
		);

		// Check if the artist is already blocked
		$already_blocked = $wpdb->get_var(
			$prepared_command
		);

		$already_blocked = $already_blocked ? absint( $already_blocked ) : 0;

		return (bool) $already_blocked;
	}

	public function block( int $artist_id ): bool|WP_Error
	{
		global $wpdb;

		// Check if the artist exists.
		if ( ! $this->exists() ) {
			return new WP_Error( 'artist_not_found', 'Artist does not exist' );
		}

		// Check if the artist is already blocked.
		if ( $this->alreadyBlocked( $artist_id ) ) {
			return new WP_Error( 'already_blocked', "You can't block artist that is already blocked" );
		}

		// Insert a new entry into the artist_blocks table.
		$result = $wpdb->insert(
			$this->artist_blocks_table,
			[
				'blocker_id' => $this->id,
				'blocked_id' => $artist_id,
			],
			[
				'%d',
				'%d',
			]
		);

		// Check if the row was inserted successfully
		if ( ! $result ) {
			return new WP_Error( 'block_failed', 'Failed to block the artist' );
		}

		return true;
	}

	public function unblock( int $artist_id ): bool|WP_Error
	{
		global $wpdb;

		// Check if the artist exists.
		if ( ! $this->exists() ) {
			return new WP_Error( 'artist_not_found', 'Artist does not exist' );
		}

		// Check if the artist hasn't been blocked.
		if ( ! $this->alreadyBlocked( $artist_id ) ) {
			return new WP_Error( 'has_not_been_blocked', "You can't unblock artists that haven't been blocked" );
		}

		// Insert a new entry into the artist_blocks table.
		$result = $wpdb->delete(
			$this->artist_blocks_table,
			[
				'blocker_id' => $this->id,
				'blocked_id' => $artist_id,
			],
			[
				'%d',
				'%d',
			]
		);

		// Check if the row was inserted successfully
		if ( ! $result ) {
			return new WP_Error( 'unblock_failed', 'Failed to unblock the artist' );
		}

		return true;
	}

	public function totalPhotos(): int
	{
		if ( $this->id === null ) {
			trigger_error( 'The user_id must be set in constructor when calling total_photos method.', E_USER_WARNING );

			return 0;
		}

		global $wpdb;

		$photo_attributes_table = $wpdb->prefix . 'pm_photo_attributes';

		$query_command = "
			SELECT COUNT(*)
			FROM $wpdb->posts P
			JOIN $photo_attributes_table PA ON (
				PA.photo_id = P.ID
			)
			WHERE P.post_type = 'attachment'
			AND P.post_author = %d
		";

		$prepared_command = $wpdb->prepare( $query_command, $this->id );

		$total_photos = $wpdb->get_var( $prepared_command );

		return ( is_numeric( $total_photos ) ) ? (int) $total_photos : 0;
	}

	public function totalArchivedPhotos(): int
	{
		return $this->totalPhotosByPostStatus( 'archive' );
	}

	public function totalUnarchivedPhotos(): int
	{
		return $this->totalPhotosByPostStatus( 'inherit' );
	}

	public function totalPhotosByPostStatus( string $post_status ): int
	{

		if ( $this->id === null ) {
			trigger_error( 'The "id" must be set in constructor when calling totalPhotosByPostStatus method.', E_USER_WARNING );

			return 0;
		}

		global $wpdb;

		$query_command = "
			SELECT COUNT(*) FROM $wpdb->posts
			WHERE post_type = 'attachment'
			AND post_author = %d
			AND post_status = %s
		";

		$prepared_command = $wpdb->prepare( $query_command, $this->id, $post_status );

		$total_photos = $wpdb->get_var( $prepared_command );

		return ( is_numeric( $total_photos ) ) ? (int) $total_photos : 0;

	}

	/**
	 * Get the artist's album list.
	 *
	 * @return AlbumData[] Array of `AlbumData` instances.
	 */
	public function albums(): array
	{

		$artist_id = $this->id;

		$artist_attribute = ArtistAttributeTable::withId( $artist_id );

		$albums = $artist_attribute->value( 'albums' );
		$data   = [];

		foreach ( $albums as $album_slug => $album_name ) {
			if ( $album_slug === 'all-photos' ) {
				$totalPhotos = ( new Album( $artist_id ) )->totalUnarchivedPhotos();
			} else {
				$totalPhotos = ( new Album( $artist_id, $album_slug ) )->totalUnarchivedPhotos();
			}

			$data[] = new AlbumData(
				slug: $album_slug,
				text: $album_name,
				totalPhotos: $totalPhotos,
			);
		}

		return $data;

	}

	public function reachedDailyUploadsLimit(): bool
	{
		if ( $this->id === null ) {
			trigger_error( 'The "id" must be set in constructor when calling reachedDailyUploadsLimit method.', E_USER_WARNING );

			return false;
		}

		$upload_limit_perday = PmConfig::dailyUploadsLimit();
		$total_uploads_today = $this->totalUploadsToday();

		return $total_uploads_today >= $upload_limit_perday;
	}

	public function totalUploadsToday(): int
	{
		if ( $this->id === null ) {
			trigger_error( 'The "id" must be set in constructor when calling totalUploadsToday method.', E_USER_WARNING );

			return 0;
		}

		global $wpdb;

		$query_command = "
			SELECT COUNT(*) FROM $wpdb->posts
			WHERE post_type = 'attachment'
			AND post_author = %d
			AND post_date >= %s
		";

		$prepared_command = $wpdb->prepare(
			$query_command,
			$this->id,
			gmdate( 'Y-m-d 00:00:00' )
		);

		$total_photos = $wpdb->get_var( $prepared_command );

		return ( is_numeric( $total_photos ) ) ? (int) $total_photos : 0;
	}

	public function totalFollowing(): int|WP_Error
	{
		if ( $this->id === null ) {
			trigger_error( 'The "id" must be set in constructor when calling totalFollowing method.', E_USER_WARNING );

			return 0;
		}

		$artist_attribute = ArtistAttributeTable::withId( $this->id );
		$followed_users   = $artist_attribute->value( 'followed_users' );

		return count( $followed_users );
	}

	public function totalFollowers( ?int $artistId = null ): int|WP_Error
	{
		if ( is_null( $artistId ) && is_null( $this->id ) ) {
			return new WP_Error(
				'missing_artist_id',
				'If $artistId is not provided, then it must be set (as $id) in the constructor when calling totalFollowers method.'
			);
		}

		$user_id = is_null( $artistId ) ? $this->id : $artistId;

		global $wpdb;

		$artist_attributes_table = $wpdb->prefix . 'pm_artist_attributes';

		$query_command = "
			SELECT COUNT(*)
			FROM $artist_attributes_table
			WHERE JSON_CONTAINS(
				followed_users,
				%s,
				'$'
			)
		";

		$prepared_command = $wpdb->prepare( $query_command, $user_id );

		$total = $wpdb->get_var( $prepared_command );

		if ( $total === null ) {
			return new WP_Error(
				'get_var_failed',
				'Failed to get total followers'
			);
		}

		return absint( $total );
	}

	public function follow(): FollowResult|WP_Error
	{
		$user = $this->followUnfollowValidation();

		if ( is_wp_error( $user ) ) {
			return $user;
		}

		$follower_attribute = ArtistAttributeTable::withId( $this->followerId );

		$followed_users = $follower_attribute->value( 'followed_users' );
		$is_following   = in_array( $this->id, $followed_users );

		if ( $is_following ) {
			return new WP_Error(
				'already_following',
				"You already following this artist before"
			);
		}

		$followed_users[] = $this->id;
		$follower_attribute->updateColumn( 'followed_users', $followed_users );

		$artist_total_followers = ( new ArtistUtil( $this->id ) )->totalFollowers();
		$artist_total_followers = is_wp_error( $artist_total_followers ) ? 0 : $artist_total_followers;

		$artist_total_following = ( new ArtistUtil( $this->id ) )->totalFollowing();
		$artist_total_following = is_wp_error( $artist_total_following ) ? 0 : $artist_total_following;

		do_action( 'pm_follow_user', $this->followerId, $this->id );

		$follower_total_followers = ( new ArtistUtil( $this->followerId ) )->totalFollowers();
		$follower_total_followers = is_wp_error( $follower_total_followers ) ? 0 : $follower_total_followers;

		return new FollowResult(
			artistTotalFollowing: $artist_total_following,
			artistTotalFollowers: $artist_total_followers,
			followerTotalFollowing: count( $followed_users ) + 1,
			followerTotalFollowers: $follower_total_followers,
		);
	}

	public function unfollow(): FollowResult|WP_Error
	{
		$user = $this->followUnfollowValidation();

		if ( is_wp_error( $user ) ) {
			return $user;
		}

		$follower_attribute = ArtistAttributeTable::withId( $this->followerId );

		$followed_users = $follower_attribute->value( 'followed_users' );
		$is_following   = in_array( $this->id, $followed_users );

		if ( ! $is_following ) {
			return new WP_Error(
				'has_not_been_following',
				"You haven't followed this artist before"
			);
		}

		$followed_users = array_diff( $followed_users, [ $this->id ] );
		$followed_users = array_values( $followed_users );

		$follower_attribute->updateColumn( 'followed_users', $followed_users );

		$artist_total_followers = ( new ArtistUtil( $this->id ) )->totalFollowers();
		$artist_total_followers = is_wp_error( $artist_total_followers ) ? 0 : $artist_total_followers;

		$artist_total_following = ( new ArtistUtil( $this->id ) )->totalFollowing();
		$artist_total_following = is_wp_error( $artist_total_following ) ? 0 : $artist_total_following;

		do_action( 'pm_unfollow_user', $this->followerId, $this->id );

		$follower_total_followers = ( new ArtistUtil( $this->followerId ) )->totalFollowers();
		$follower_total_followers = is_wp_error( $follower_total_followers ) ? 0 : $follower_total_followers;

		return new FollowResult(
			artistTotalFollowing: $artist_total_following,
			artistTotalFollowers: $artist_total_followers,
			followerTotalFollowing: count( $followed_users ) - 1,
			followerTotalFollowers: $follower_total_followers,
		);
	}

	public function followUnfollowValidation(): WP_User|WP_Error
	{
		if ( $this->id === null ) {
			return new WP_Error(
				'missing_artist_id',
				'The artist id must be set (as "id") in constructor when calling like / unlike method.'
			);
		}

		$user = get_userdata( $this->id );

		if ( ! $user ) {
			return new WP_Error(
				'user_not_found',
				"User doesn't exist"
			);
		}

		return $user;
	}
}
