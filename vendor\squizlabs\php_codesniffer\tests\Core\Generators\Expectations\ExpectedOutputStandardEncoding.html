<html>
 <head>
  <title>GeneratorTest Coding Standards</title>
  <style>
        body {
            background-color: #FFFFFF;
            font-size: 14px;
            font-family: Arial, Helvetica, sans-serif;
            color: #000000;
        }

        h1 {
            color: #666666;
            font-size: 20px;
            font-weight: bold;
            margin-top: 0px;
            background-color: #E6E7E8;
            padding: 20px;
            border: 1px solid #BBBBBB;
        }

        h2 {
            color: #00A5E3;
            font-size: 16px;
            font-weight: normal;
            margin-top: 50px;
        }

        .code-comparison {
            width: 100%;
        }

        .code-comparison td {
            border: 1px solid #CCCCCC;
        }

        .code-comparison-title, .code-comparison-code {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px;
            color: #000000;
            vertical-align: top;
            padding: 4px;
            width: 50%;
            background-color: #F1F1F1;
            line-height: 15px;
        }

        .code-comparison-title {
            text-align: left;
            font-weight: 600;
        }

        .code-comparison-code {
            font-family: Courier;
            background-color: #F9F9F9;
        }

        .code-comparison-highlight {
            background-color: #DDF1F7;
            border: 1px solid #00A5E3;
            line-height: 15px;
        }

        .tag-line {
            text-align: center;
            width: 100%;
            margin-top: 30px;
            font-size: 12px;
        }

        .tag-line a {
            color: #000000;
        }
    </style>
 </head>
 <body>
  <h1>GeneratorTest Coding Standards</h1>
  <a name="Standard-Element,-handling-of-HTML-tags" />
  <h2>Standard Element, handling of HTML tags</h2>
  <p class="text">The use of <em>tags</em> in standard descriptions is allowed and their handling should be <em>safeguarded</em>.<br/>
Other tags, like &lt;a href=&quot;example.com&quot;&gt;link&lt;/a&gt;, &lt;b&gt;bold&lt;/bold&gt;, &lt;script&gt;&lt;/script&gt; are not allowed and will be encoded for display when the HTML or Markdown report is used.</p>
  <div class="tag-line">Documentation generated on #REDACTED# by <a href="https://github.com/PHPCSStandards/PHP_CodeSniffer">PHP_CodeSniffer #VERSION#</a></div>
 </body>
</html>
