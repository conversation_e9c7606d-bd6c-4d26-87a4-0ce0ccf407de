<?php

declare( strict_types = 1 );

namespace Pm\Api\Controllers;

use Pm\Module\Artist\Profile;
use Pm\Module\Artist\Queries\ArtistQuery;
use Pm\Module\Artist\Table\ArtistAttributeTable;
use Pm\Module\Avatar\Utils\AvatarUtil;
use WP_REST_Request;
use WP_REST_Response;

final class MyProfileController
{
	/**
	 * Get current artist's `ProfileData`.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function get( WP_REST_Request $request ): WP_REST_Response
	{

		$artist_id = get_current_user_id();

		$data = [
			'success' => true,
			'message' => __( 'Profile data retrieved.', 'pm' ),
			'data'    => Profile::find( $artist_id ),
		];

		return new WP_REST_Response( $data, 200 );

	}

	/**
	 * Edit current artist's profile.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function edit( WP_REST_Request $request ): WP_REST_Response
	{

		$artist_id = get_current_user_id();
		$artist    = pm_find_artist( id: $artist_id, asProfileData: true );
		$nicename  = $artist->nicename;

		$email        = sanitize_text_field( $request->get_param( 'email' ) );
		$instagram    = sanitize_text_field( $request->get_param( 'instagram' ) );
		$firstName    = sanitize_text_field( $request->get_param( 'first_name' ) );
		$lastName     = sanitize_text_field( $request->get_param( 'last_name' ) );
		$location     = sanitize_text_field( $request->get_param( 'location' ) );
		$location     = ucwords( strtolower( $location ) );
		$location_lat = sanitize_text_field( $request->get_param( 'location_lat' ) );
		$location_lng = sanitize_text_field( $request->get_param( 'location_lng' ) );
		$website      = sanitize_text_field( $request->get_param( 'website' ) );
		$camera       = sanitize_text_field( $request->get_param( 'camera' ) );
		$focalLength  = sanitize_text_field( $request->get_param( 'focal_length' ) );
		$description  = sanitize_text_field( $request->get_param( 'description' ) );

		$full_name = pm_artist_full_name( $artist_id, 'array', [
			'first_name'    => ucwords( strtolower( $firstName ) ),
			'last_name'     => ucwords( strtolower( $lastName ) ),
			'user_nicename' => $nicename,
		] );

		wp_update_user(
			[
				'ID'           => $artist_id,
				'user_email'   => $email,
				'user_url'     => esc_url( $website ),
				'display_name' => $full_name,
				'first_name'   => $firstName,
				'last_name'    => $lastName,
			]
		);

		// Update ACF location field.
		update_field(
			'google_location',
			[
				'lat'     => $location_lat,
				'lng'     => $location_lng,
				'address' => $location,
				'zoom'    => 14
			],
			'user_' . $artist_id
		);

		$artist_attribute = ArtistAttributeTable::withId( $artist_id );

		$artist_attribute->updateColumns( [
			'first_name'   => $firstName,
			'last_name'    => $lastName,
			'location'     => $location,
			'location_lat' => $location_lat,
			'location_lng' => $location_lng,
			'instagram'    => $instagram,
			'camera'       => $camera,
			'focal_length' => $focalLength,
			'description'  => $description,
		] );

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => 'Artist data updated',
				'data'    => Profile::find( $artist_id ),
			],
			200
		);

	}

	/**
	 * Upload current artist's avatar.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function uploadAvatar( WP_REST_Request $request ): WP_REST_Response
	{

		$artist_id = get_current_user_id();
		$fields    = $request->get_file_params();
		$avatar    = $fields['avatar'] ?? null;

		if ( ! $avatar ) {
			return new WP_REST_Response(
				[
					'success' => false,
					'message' => 'Please upload an image',
				],
				400
			);
		}

		$avatar_url = AvatarUtil::fromArtistId( $artist_id )->upload();

		if ( is_wp_error( $avatar_url ) ) {
			return new WP_REST_Response( [
				'success' => false,
				'message' => $avatar_url->get_error_message(),
				'data'    => null,
			] );
		}

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => 'Avatar uploaded',
				'data'    => Profile::find( $artist_id ),
			],
			200
		);

	}

	/**
	 * Delete current artist's avatar.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function deleteAvatar( WP_REST_Request $request ): WP_REST_Response
	{

		$artist_id = get_current_user_id();

		AvatarUtil::fromArtistId( $artist_id )->delete();

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => 'Avatar deleted',
				'data'    => Profile::find( $artist_id ),
			],
			200
		);

	}

	/**
	 * Current artist's liked photos.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function likedPhotos( WP_REST_Request $request ): WP_REST_Response
	{

		$artist_id = get_current_user_id();

		$artist = pm_find_artist( $artist_id );

		if ( ! $artist ) {
			return new WP_REST_Response(
				[
					'success' => false,
					'message' => 'Artist not found',
				],
				404
			);
		}

		$limit   = absint( $request->get_param( 'limit' ) );
		$last_id = absint( $request->get_param( 'last_id' ) );
		$last_id = 0 === $last_id ? null : $last_id;

		$liked_photos = pm_fetch_liked_photos(
			artistId: $artist_id,
			limit: $limit,
			lastId: $last_id,
			useAvatar: true,
		);

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => 'Artist data retrieved successfully.',
				'data'    => $liked_photos,
			],
			200
		);

	}

	/**
	 * Current artist's archived photos.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function archivedPhotos( WP_REST_Request $request ): WP_REST_Response
	{

		$artist_id = get_current_user_id();

		$artist = pm_find_artist( $artist_id );

		if ( ! $artist ) {
			return new WP_REST_Response(
				[
					'success' => false,
					'message' => 'Artist not found',
				],
				404
			);
		}

		$limit   = absint( $request->get_param( 'limit' ) );
		$last_id = absint( $request->get_param( 'last_id' ) );
		$last_id = 0 === $last_id ? null : $last_id;

		$archived_photos = pm_fetch_archived_photos(
			artistId: $artist_id,
			limit: $limit,
			lastId: $last_id,
			useAvatar: true,
		);

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => 'Artist data retrieved successfully.',
				'data'    => $archived_photos,
			],
			200
		);

	}

	/**
	 * REST API handler to get current user's followed artists.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function followings( WP_REST_Request $request ): WP_REST_Response
	{

		$limit = absint( $request->get_param( 'limit' ) );

		$last_id = $request->get_param( 'last_id' );
		$last_id = is_numeric( $last_id ) && $last_id > - 1 ? (int) $last_id : null;
		$last_id = is_int( $last_id ) && $last_id > - 1 ? $last_id : null;

		$last_total_photos = $request->get_param( 'last_total_photos' );
		$last_total_photos = is_numeric( $last_total_photos ) && $last_total_photos > - 1 ? (int) $last_total_photos : null;
		$last_total_photos = is_int( $last_total_photos ) && $last_total_photos > - 1 ? $last_total_photos : null;

		$current_user_id  = get_current_user_id();
		$artist_attribute = ArtistAttributeTable::withId( $current_user_id );

		$followed_user_ids = $artist_attribute->value( 'followed_users' );

		$include_ids = empty( $followed_user_ids ) || ! is_array( $followed_user_ids ) ? [ - 2 ] : $followed_user_ids;

		if ( is_array( $include_ids ) ) {
			$include_ids = implode( ',', $include_ids );
		}

		$artist_query = new ArtistQuery();

		$artists = $artist_query->fetch(
			limit: $limit,
			lastId: $last_id,
			lastTotalPhotos: $last_total_photos,
			includeIds: $include_ids,
			useDescription: true,
			useFollowingStatus: true,
			useTotalFollowing: true,
			useTotalFollowers: true,
		);

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => count( $artists ) . ' artists returned',
				'data'    => $artists,
			],
			200
		);

	}

	/**
	 * REST API handler to get current user's followers.
	 *
	 * @param WP_REST_Request $request The request.
	 *
	 * @return WP_REST_Response The response.
	 */
	public function followers( WP_REST_Request $request ): WP_REST_Response
	{

		$limit = absint( $request->get_param( 'limit' ) );

		$last_id = $request->get_param( 'last_id' );
		$last_id = is_numeric( $last_id ) && $last_id > - 1 ? (int) $last_id : null;
		$last_id = is_int( $last_id ) && $last_id > - 1 ? $last_id : null;

		$last_total_photos = $request->get_param( 'last_total_photos' );
		$last_total_photos = is_numeric( $last_total_photos ) && $last_total_photos > - 1 ? (int) $last_total_photos : null;
		$last_total_photos = is_int( $last_total_photos ) && $last_total_photos > - 1 ? $last_total_photos : null;

		// TO AUGMENTCODE: START YOUR EDITS HERE.

		$current_user_id  = get_current_user_id();

		$artist_query = new ArtistQuery();

		$artists = [];

		// TO AUGMENTCODE: STOP YOUR EDITS HERE.

		return new WP_REST_Response(
			[
				'success' => true,
				'message' => count( $artists ) . ' artists returned',
				'data'    => $artists,
			],
			200
		);

	}
}
