{"packages": [{"name": "contactjavas/modern-wpcs-ruleset", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/contactjavas/modern-wpcs-ruleset.git", "reference": "396d67fd00e9bf71652eb0968d7394bdc3062984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contactjavas/modern-wpcs-ruleset/zipball/396d67fd00e9bf71652eb0968d7394bdc3062984", "reference": "396d67fd00e9bf71652eb0968d7394bdc3062984", "shasum": ""}, "require": {"contactjavas/modern-wpcs-standard": "dev-master", "php": ">=8.1", "sirbrillig/phpcs-import-detection": "^1.3.3", "sirbrillig/phpcs-variable-analysis": "^2.11.18", "wp-coding-standards/wpcs": "^3.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "squizlabs/php_codesniffer": "^3.9.1"}, "time": "2024-04-21T11:06:44+00:00", "default-branch": true, "type": "phpcodesniffer-standard", "installation-source": "dist", "license": ["MIT"], "description": "A PHPCS meta-ruleset for modern WordPress development", "keywords": ["phpcs", "static analysis"], "support": {"source": "https://github.com/contactjavas/modern-wpcs-ruleset/tree/master"}, "install-path": "../contactjavas/modern-wpcs-ruleset"}, {"name": "contactjavas/modern-wpcs-standard", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/contactjavas/modern-wpcs-standard.git", "reference": "7da2bdfbd26fc4e69183497c078017b9c712ba5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contactjavas/modern-wpcs-standard/zipball/7da2bdfbd26fc4e69183497c078017b9c712ba5e", "reference": "7da2bdfbd26fc4e69183497c078017b9c712ba5e", "shasum": ""}, "require": {"php": "^8.1 || ^8.2 || ^8.3", "squizlabs/php_codesniffer": "^3.9"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "phpunit/phpunit": "^10.0 || ^11.0", "sirbrillig/phpcs-variable-analysis": "^2.11.18"}, "time": "2024-04-21T11:35:59+00:00", "default-branch": true, "type": "phpcodesniffer-standard", "installation-source": "dist", "autoload": {"psr-4": {"ModernWpcsStandard\\": "ModernWpcsStandard/"}}, "autoload-dev": {"psr-4": {"ModernWpcsStandard\\Tests\\": "tests/"}}, "scripts": {"test": ["./vendor/bin/phpunit --testdox"], "lint": ["./vendor/bin/phpcs ModernWpcsStandard"]}, "license": ["MIT"], "authors": [{"name": "Bagus", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A set of phpcs sniffs for modern php development.", "keywords": ["phpcs", "static analysis"], "support": {"issues": "https://github.com/contactjavas/modern-wpcs-standard/issues", "wiki": "https://github.com/contactjavas/modern-wpcs-standard/wiki", "source": "https://github.com/contactjavas/modern-wpcs-standard"}, "install-path": "../contactjavas/modern-wpcs-standard"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git", "reference": "4be43904336affa5c2f70744a348312336afd0da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/4be43904336affa5c2f70744a348312336afd0da", "reference": "4be43904336affa5c2f70744a348312336afd0da", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "time": "2023-01-05T11:28:13+00:00", "type": "composer-plugin", "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "source": "https://github.com/PHPCSStandards/composer-installer"}, "install-path": "../dealerdirect/phpcodesniffer-composer-installer"}, {"name": "phpcsstandards/phpcsextra", "version": "dev-develop", "version_normalized": "dev-develop", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "cf28c840b04985ef6a9aabb15ef14dd7b9d3b550"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/cf28c840b04985ef6a9aabb15ef14dd7b9d3b550", "reference": "cf28c840b04985ef6a9aabb15ef14dd7b9d3b550", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.0.9", "squizlabs/php_codesniffer": "^3.8.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "time": "2025-03-03T01:39:12+00:00", "default-branch": true, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../phpcsstandards/phpcsextra"}, {"name": "phpcsstandards/phpcsutils", "version": "dev-develop", "version_normalized": "dev-develop", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "060222e14c567c00fc4fd056ec7af809810c1fbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/060222e14c567c00fc4fd056ec7af809810c1fbc", "reference": "060222e14c567c00fc4fd056ec7af809810c1fbc", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.10.1 || 4.0.x-dev@dev"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0 || ^3.0.0"}, "time": "2025-02-15T23:56:59+00:00", "default-branch": true, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../phpcsstandards/phpcsutils"}, {"name": "sirbrillig/phpcs-import-detection", "version": "v1.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sirbrillig/phpcs-import-detection.git", "reference": "7035ef6f3a15db182b59664d2c060918aa827e16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sirbrillig/phpcs-import-detection/zipball/7035ef6f3a15db182b59664d2c060918aa827e16", "reference": "7035ef6f3a15db182b59664d2c060918aa827e16", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "squizlabs/php_codesniffer": "^3.5.8"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.1", "phpunit/phpunit": "^9.0", "sirbrillig/phpcs-variable-analysis": "^2.0.1"}, "time": "2022-10-30T19:04:13+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "autoload": {"psr-4": {"ImportDetection\\": "ImportDetection/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A set of phpcs sniffs to look for unused or unimported symbols.", "keywords": ["phpcs", "static analysis"], "support": {"issues": "https://github.com/sirbrillig/phpcs-import-detection/issues", "source": "https://github.com/sirbrillig/phpcs-import-detection/tree/v1.3.3"}, "install-path": "../sirbrillig/phpcs-import-detection"}, {"name": "sirbrillig/phpcs-variable-analysis", "version": "2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/sirbrillig/phpcs-variable-analysis.git", "reference": "ffb6f16c6033ec61ed84446b479a31d6529f0eb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sirbrillig/phpcs-variable-analysis/zipball/ffb6f16c6033ec61ed84446b479a31d6529f0eb7", "reference": "ffb6f16c6033ec61ed84446b479a31d6529f0eb7", "shasum": ""}, "require": {"php": ">=5.4.0", "squizlabs/php_codesniffer": "^3.5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "phpcsstandards/phpcsdevcs": "^1.1", "phpstan/phpstan": "^1.7", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.5 || ^7.0 || ^8.0 || ^9.0 || ^10.5.32 || ^11.3.3", "vimeo/psalm": "^0.2 || ^0.3 || ^1.1 || ^4.24 || ^5.0"}, "time": "2025-01-06T17:54:24+00:00", "default-branch": true, "type": "phpcodesniffer-standard", "installation-source": "dist", "autoload": {"psr-4": {"VariableAnalysis\\": "VariableAnalysis/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHPCS sniff to detect problems with variables.", "keywords": ["phpcs", "static analysis"], "support": {"issues": "https://github.com/sirbrillig/phpcs-variable-analysis/issues", "source": "https://github.com/sirbrillig/phpcs-variable-analysis", "wiki": "https://github.com/sirbrillig/phpcs-variable-analysis/wiki"}, "install-path": "../sirbrillig/phpcs-variable-analysis"}, {"name": "squizlabs/php_codesniffer", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "8894b66352e915bba37889932edb980d6a4c5352"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/8894b66352e915bba37889932edb980d6a4c5352", "reference": "8894b66352e915bba37889932edb980d6a4c5352", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "time": "2025-03-08T19:04:35+00:00", "default-branch": true, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "install-path": "../squizlabs/php_codesniffer"}, {"name": "wp-coding-standards/wpcs", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/9333efcbff231f10dfd9c56bb7b65818b4733ca7", "reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "shasum": ""}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.2.1", "phpcsstandards/phpcsutils": "^1.0.10", "squizlabs/php_codesniffer": "^3.9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "time": "2024-03-25T16:39:00+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "funding": [{"url": "https://opencollective.com/php_codesniffer", "type": "custom"}], "install-path": "../wp-coding-standards/wpcs"}], "dev": true, "dev-package-names": ["contactjavas/modern-wpcs-ruleset", "contactjavas/modern-wpcs-standard", "dealerdirect/phpcodesniffer-composer-installer", "phpcsstandards/phpcsextra", "phpcsstandards/phpcsutils", "sirbrillig/phpcs-import-detection", "sirbrillig/phpcs-variable-analysis", "squizlabs/php_codesniffer", "wp-coding-standards/wpcs"]}