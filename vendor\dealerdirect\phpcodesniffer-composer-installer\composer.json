{"name": "dealerdirect/phpcodesniffer-composer-installer", "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "type": "composer-plugin", "keywords": ["composer", "installer", "plugin", "phpcs", "phpcbf", "codesniffer", "php<PERSON><PERSON><PERSON>", "php_codesniffer", "standard", "standards", "style guide", "stylecheck", "qa", "quality", "code quality", "tests"], "homepage": "http://www.dealerdirect.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "source": "https://github.com/PHPCSStandards/composer-installer"}, "require": {"php": ">=5.4", "composer-plugin-api": "^1.0 || ^2.0", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"ext-json": "*", "ext-zip": "*", "composer/composer": "*", "phpcompatibility/php-compatibility": "^9.0", "php-parallel-lint/php-parallel-lint": "^1.3.1", "yoast/phpunit-polyfills": "^1.0"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "autoload-dev": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Tests\\": "tests/"}}, "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "scripts": {"install-codestandards": ["PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin::run"], "lint": ["@php ./vendor/php-parallel-lint/php-parallel-lint/parallel-lint . -e php --exclude vendor --exclude .git"], "test": ["@php ./vendor/phpunit/phpunit/phpunit --no-coverage"], "coverage": ["@php ./vendor/phpunit/phpunit/phpunit"]}}